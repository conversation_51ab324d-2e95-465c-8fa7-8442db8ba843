// lib/services/adminReferralService.ts
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Referral } from '@/models/Referral';
import { Investment } from '@/models/Investment';
import mongoose from 'mongoose';

export interface AdminReferralData {
  _id: string;
  referrer: {
    _id: string;
    name: string;
    email: string;
    referralCode: string;
  };
  referredUser: {
    _id: string;
    name: string;
    email: string;
    referralCode: string;
  } | null;
  referralId: string;
  createdAt: Date;
  active: boolean;
  earnings: {
    investmentAmount: number;
    percentage: number;
    earningAmount: number;
  }[];
  totalEarnings: number;
  investmentCount: number;
}

export interface AdminReferralFilters {
  search?: string;
  status?: 'all' | 'active' | 'inactive';
  hasReferredUser?: boolean;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface AdminReferralResponse {
  referrals: AdminReferralData[];
  totalCount: number;
  totalPages: number;
  currentPage: number;
  hasNextPage: boolean;
  hasPrevPage: boolean;
  stats: {
    totalReferrals: number;
    activeReferrals: number;
    inactiveReferrals: number;
    referralsWithUsers: number;
    pendingReferrals: number;
    totalEarnings: number;
  };
}

export class AdminReferralService {
  
  /**
   * Get all referrals for admin dashboard with comprehensive data
   */
  static async getAllReferrals(filters: AdminReferralFilters = {}): Promise<AdminReferralResponse> {
    await connectToDatabase();

    const {
      search = '',
      status = 'all',
      page = 1,
      pageSize = 10,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = filters;

    console.log('🔍 AdminReferralService: Fetching all referrals with filters:', filters);

    try {
      // Build aggregation pipeline for comprehensive referral data
      const pipeline: any[] = [
        // Lookup referrer details
        {
          $lookup: {
            from: 'users',
            localField: 'referrerId',
            foreignField: '_id',
            as: 'referrerDetails'
          }
        },
        // Lookup referred user details (if exists)
        {
          $lookup: {
            from: 'users',
            localField: 'referredUserId',
            foreignField: '_id',
            as: 'referredUserDetails'
          }
        },
        // Unwind referrer (required)
        {
          $unwind: {
            path: '$referrerDetails',
            preserveNullAndEmptyArrays: false
          }
        },
        // Unwind referred user (optional)
        {
          $unwind: {
            path: '$referredUserDetails',
            preserveNullAndEmptyArrays: true
          }
        },
        // Lookup investments for referred user to calculate earnings
        {
          $lookup: {
            from: 'investments',
            let: { referredUserId: '$referredUserId' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$userId', '$$referredUserId'] },
                      { $eq: ['$paymentStatus', 'approved'] }
                    ]
                  }
                }
              }
            ],
            as: 'referredUserInvestments'
          }
        },
        // Add computed fields
        {
          $addFields: {
            // Calculate if referral is active (has investments)
            active: {
              $gt: [{ $size: '$referredUserInvestments' }, 0]
            },
            // Calculate total earnings (10% of investments)
            totalEarnings: {
              $multiply: [
                { $sum: '$referredUserInvestments.amount' },
                0.1
              ]
            },
            // Count investments
            investmentCount: { $size: '$referredUserInvestments' },
            // Create earnings array
            earnings: {
              $map: {
                input: '$referredUserInvestments',
                as: 'investment',
                in: {
                  investmentAmount: '$$investment.amount',
                  percentage: 10,
                  earningAmount: { $multiply: ['$$investment.amount', 0.1] }
                }
              }
            }
          }
        }
      ];

      // Add search filter
      if (search) {
        pipeline.push({
          $match: {
            $or: [
              { 'referrerDetails.name': { $regex: search, $options: 'i' } },
              { 'referrerDetails.email': { $regex: search, $options: 'i' } },
              { 'referredUserDetails.name': { $regex: search, $options: 'i' } },
              { 'referredUserDetails.email': { $regex: search, $options: 'i' } },
              { 'referralId': { $regex: search, $options: 'i' } }
            ]
          }
        });
      }

      // Add status filter
      if (status !== 'all') {
        pipeline.push({
          $match: {
            active: status === 'active'
          }
        });
      }

      // Get total count before pagination
      const countPipeline = [...pipeline, { $count: 'total' }];
      const countResult = await Referral.aggregate(countPipeline);
      const totalCount = countResult[0]?.total || 0;

      // Add sorting
      const sortDirection = sortOrder === 'desc' ? -1 : 1;
      pipeline.push({
        $sort: { [sortBy]: sortDirection }
      });

      // Add pagination for non-export requests
      if (pageSize < 1000) {
        pipeline.push(
          { $skip: (page - 1) * pageSize },
          { $limit: pageSize }
        );
      }

      // Project final structure
      pipeline.push({
        $project: {
          _id: 1,
          referrer: {
            _id: '$referrerDetails._id',
            name: '$referrerDetails.name',
            email: '$referrerDetails.email',
            referralCode: '$referrerDetails.referralCode'
          },
          referredUser: {
            $cond: {
              if: { $ne: ['$referredUserDetails', null] },
              then: {
                _id: '$referredUserDetails._id',
                name: '$referredUserDetails.name',
                email: '$referredUserDetails.email',
                referralCode: '$referredUserDetails.referralCode'
              },
              else: null
            }
          },
          referralId: 1,
          createdAt: 1,
          active: 1,
          earnings: 1,
          totalEarnings: 1,
          investmentCount: 1
        }
      });

      console.log('📊 Executing aggregation pipeline with', pipeline.length, 'stages');
      
      // Execute aggregation
      const referrals = await Referral.aggregate(pipeline);
      
      console.log(`✅ Found ${referrals.length} referrals out of ${totalCount} total`);

      // Calculate statistics
      const stats = await this.calculateReferralStats();

      // Calculate pagination info
      const totalPages = Math.ceil(totalCount / pageSize);
      const hasNextPage = page < totalPages;
      const hasPrevPage = page > 1;

      return {
        referrals,
        totalCount,
        totalPages,
        currentPage: page,
        hasNextPage,
        hasPrevPage,
        stats
      };

    } catch (error) {
      console.error('❌ Error in AdminReferralService.getAllReferrals:', error);
      throw new Error(`Failed to fetch referrals: ${(error as Error).message}`);
    }
  }

  /**
   * Get referral statistics for admin dashboard
   */
  static async calculateReferralStats() {
    try {
      const [
        totalReferrals,
        activeReferrals,
        referralsWithUsers,
        totalEarningsResult
      ] = await Promise.all([
        // Total referrals count
        Referral.countDocuments(),
        
        // Active referrals (with investments)
        Referral.aggregate([
          {
            $lookup: {
              from: 'investments',
              localField: 'referredUserId',
              foreignField: 'userId',
              as: 'investments'
            }
          },
          {
            $match: {
              'investments.paymentStatus': 'approved'
            }
          },
          { $count: 'count' }
        ]),
        
        // Referrals with referred users
        Referral.countDocuments({ referredUserId: { $ne: null } }),
        
        // Total earnings calculation
        Referral.aggregate([
          {
            $lookup: {
              from: 'investments',
              let: { referredUserId: '$referredUserId' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$userId', '$$referredUserId'] },
                        { $eq: ['$paymentStatus', 'approved'] }
                      ]
                    }
                  }
                }
              ],
              as: 'investments'
            }
          },
          {
            $addFields: {
              totalEarnings: {
                $multiply: [
                  { $sum: '$investments.amount' },
                  0.1
                ]
              }
            }
          },
          {
            $group: {
              _id: null,
              totalEarnings: { $sum: '$totalEarnings' }
            }
          }
        ])
      ]);

      return {
        totalReferrals,
        activeReferrals: activeReferrals[0]?.count || 0,
        inactiveReferrals: totalReferrals - (activeReferrals[0]?.count || 0),
        referralsWithUsers,
        pendingReferrals: totalReferrals - referralsWithUsers,
        totalEarnings: totalEarningsResult[0]?.totalEarnings || 0
      };

    } catch (error) {
      console.error('❌ Error calculating referral stats:', error);
      return {
        totalReferrals: 0,
        activeReferrals: 0,
        inactiveReferrals: 0,
        referralsWithUsers: 0,
        pendingReferrals: 0,
        totalEarnings: 0
      };
    }
  }

  /**
   * Alternative method: Get referrals from User relationships
   */
  static async getAllReferralsFromUsers(filters: AdminReferralFilters = {}): Promise<AdminReferralResponse> {
    await connectToDatabase();

    console.log('🔍 AdminReferralService: Fetching referrals from User relationships');

    try {
      // Get all users who have a referrerId (were referred by someone)
      const pipeline: any[] = [
        // Match users who were referred
        {
          $match: {
            referrerId: { $ne: null }
          }
        },
        // Lookup referrer details
        {
          $lookup: {
            from: 'users',
            localField: 'referrerId',
            foreignField: '_id',
            as: 'referrerDetails'
          }
        },
        // Unwind referrer
        {
          $unwind: '$referrerDetails'
        },
        // Lookup investments for earnings calculation
        {
          $lookup: {
            from: 'investments',
            let: { userId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$userId', '$$userId'] },
                      { $eq: ['$paymentStatus', 'approved'] }
                    ]
                  }
                }
              }
            ],
            as: 'investments'
          }
        },
        // Add computed fields
        {
          $addFields: {
            active: { $gt: [{ $size: '$investments' }, 0] },
            totalEarnings: {
              $multiply: [
                { $sum: '$investments.amount' },
                0.1
              ]
            },
            investmentCount: { $size: '$investments' },
            earnings: {
              $map: {
                input: '$investments',
                as: 'investment',
                in: {
                  investmentAmount: '$$investment.amount',
                  percentage: 10,
                  earningAmount: { $multiply: ['$$investment.amount', 0.1] }
                }
              }
            }
          }
        },
        // Project final structure
        {
          $project: {
            _id: 1,
            referrer: {
              _id: '$referrerDetails._id',
              name: '$referrerDetails.name',
              email: '$referrerDetails.email',
              referralCode: '$referrerDetails.referralCode'
            },
            referredUser: {
              _id: '$_id',
              name: '$name',
              email: '$email',
              referralCode: '$referralCode'
            },
            referralId: '$referrerDetails.referralCode',
            createdAt: '$createdAt',
            active: 1,
            earnings: 1,
            totalEarnings: 1,
            investmentCount: 1
          }
        }
      ];

      // Add search filter
      if (filters.search) {
        pipeline.splice(1, 0, {
          $match: {
            $or: [
              { 'name': { $regex: filters.search, $options: 'i' } },
              { 'email': { $regex: filters.search, $options: 'i' } },
              { 'referralCode': { $regex: filters.search, $options: 'i' } }
            ]
          }
        });
      }

      // Get total count
      const countPipeline = [...pipeline, { $count: 'total' }];
      const countResult = await User.aggregate(countPipeline);
      const totalCount = countResult[0]?.total || 0;

      // Add sorting and pagination
      const sortDirection = filters.sortOrder === 'desc' ? -1 : 1;
      pipeline.push({ $sort: { [filters.sortBy || 'createdAt']: sortDirection } });

      if (filters.pageSize && filters.pageSize < 1000) {
        pipeline.push(
          { $skip: ((filters.page || 1) - 1) * filters.pageSize },
          { $limit: filters.pageSize }
        );
      }

      const referrals = await User.aggregate(pipeline);
      
      console.log(`✅ Found ${referrals.length} user-based referrals out of ${totalCount} total`);

      // Calculate stats
      const stats = await this.calculateUserBasedReferralStats();

      return {
        referrals,
        totalCount,
        totalPages: Math.ceil(totalCount / (filters.pageSize || 10)),
        currentPage: filters.page || 1,
        hasNextPage: (filters.page || 1) < Math.ceil(totalCount / (filters.pageSize || 10)),
        hasPrevPage: (filters.page || 1) > 1,
        stats
      };

    } catch (error) {
      console.error('❌ Error in AdminReferralService.getAllReferralsFromUsers:', error);
      throw new Error(`Failed to fetch user-based referrals: ${(error as Error).message}`);
    }
  }

  /**
   * Calculate stats from user relationships
   */
  static async calculateUserBasedReferralStats() {
    try {
      const [totalUsers, usersWithReferrers, activeReferrals, totalEarnings] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ referrerId: { $ne: null } }),
        User.aggregate([
          { $match: { referrerId: { $ne: null } } },
          {
            $lookup: {
              from: 'investments',
              localField: '_id',
              foreignField: 'userId',
              as: 'investments'
            }
          },
          {
            $match: {
              'investments.paymentStatus': 'approved'
            }
          },
          { $count: 'count' }
        ]),
        User.aggregate([
          { $match: { referrerId: { $ne: null } } },
          {
            $lookup: {
              from: 'investments',
              let: { userId: '$_id' },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $and: [
                        { $eq: ['$userId', '$$userId'] },
                        { $eq: ['$paymentStatus', 'approved'] }
                      ]
                    }
                  }
                }
              ],
              as: 'investments'
            }
          },
          {
            $addFields: {
              totalEarnings: {
                $multiply: [{ $sum: '$investments.amount' }, 0.1]
              }
            }
          },
          {
            $group: {
              _id: null,
              totalEarnings: { $sum: '$totalEarnings' }
            }
          }
        ])
      ]);

      return {
        totalReferrals: usersWithReferrers,
        activeReferrals: activeReferrals[0]?.count || 0,
        inactiveReferrals: usersWithReferrers - (activeReferrals[0]?.count || 0),
        referralsWithUsers: usersWithReferrers,
        pendingReferrals: 0,
        totalEarnings: totalEarnings[0]?.totalEarnings || 0
      };
    } catch (error) {
      console.error('❌ Error calculating user-based referral stats:', error);
      return {
        totalReferrals: 0,
        activeReferrals: 0,
        inactiveReferrals: 0,
        referralsWithUsers: 0,
        pendingReferrals: 0,
        totalEarnings: 0
      };
    }
  }
}
