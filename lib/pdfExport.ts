// lib/pdfExport.ts
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Extend jsPDF type to include autoTable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: any) => jsPDF;
  }
}

interface UserData {
  name: string;
  email: string;
  phone: string;
  role: string;
  status: string;
  totalInvestments: number;
  totalInvestmentAmount: number;
  referralCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  joinDate: string;
  lastActivity: string;
}

export const generateUsersPDF = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  // Create new PDF document
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Set document properties
  doc.setProperties({
    title: 'Golden Miller - Members Report',
    subject: 'Member Management Report',
    author: 'Golden Miller System',
    creator: 'Golden Miller Admin Panel'
  });

  // Colors
  const primaryColor = [234, 179, 8]; // <PERSON> yellow
  const secondaryColor = [31, 41, 55]; // Dark gray
  const lightGray = [249, 250, 251];

  // Add header
  doc.setFillColor(...primaryColor);
  doc.rect(0, 0, 297, 25, 'F');

  // Logo/Title
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(20);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 15);
  
  doc.setFontSize(12);
  doc.setFont('helvetica', 'normal');
  doc.text('Members Management Report', 20, 20);

  // Date
  doc.setFontSize(10);
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  doc.text(`Generated: ${currentDate}`, 200, 15);

  // Reset text color
  doc.setTextColor(...secondaryColor);

  // Summary statistics
  const totalMembers = users.length;
  const activeMembers = users.filter(u => u.status === 'Active').length;
  const inactiveMembers = totalMembers - activeMembers;
  const totalInvestmentValue = users.reduce((sum, u) => sum + u.totalInvestmentAmount, 0);
  const avgInvestment = totalMembers > 0 ? totalInvestmentValue / totalMembers : 0;

  // Summary box
  doc.setFillColor(...lightGray);
  doc.rect(20, 35, 257, 25, 'F');
  doc.setDrawColor(200, 200, 200);
  doc.rect(20, 35, 257, 25, 'S');

  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('📊 EXECUTIVE SUMMARY', 25, 45);

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  
  // Summary stats in columns
  const summaryY = 52;
  doc.text(`Total Members: ${totalMembers}`, 25, summaryY);
  doc.text(`Active: ${activeMembers}`, 80, summaryY);
  doc.text(`Inactive: ${inactiveMembers}`, 120, summaryY);
  doc.text(`Total Investment: $${totalInvestmentValue.toLocaleString()}`, 170, summaryY);
  doc.text(`Avg Investment: $${Math.round(avgInvestment).toLocaleString()}`, 240, summaryY);

  // Add filters info if any
  if (filters && (filters.search || filters.role !== 'all' || filters.status !== 'all')) {
    doc.setFontSize(9);
    doc.setTextColor(100, 100, 100);
    let filterText = 'Filters Applied: ';
    if (filters.search) filterText += `Search: "${filters.search}" `;
    if (filters.role && filters.role !== 'all') filterText += `Role: ${filters.role} `;
    if (filters.status && filters.status !== 'all') filterText += `Status: ${filters.status}`;
    doc.text(filterText, 25, 57);
    doc.setTextColor(...secondaryColor);
  }

  // Prepare table data
  const tableHeaders = [
    'Member Name',
    'Email',
    'Phone',
    'Role',
    'Status',
    'Investments',
    'Amount ($)',
    'Referral Code',
    'Join Date',
    'Last Activity'
  ];

  const tableData = users.map((user, index) => [
    user.name,
    user.email,
    user.phone || 'N/A',
    user.role.toUpperCase(),
    user.status,
    user.totalInvestments.toString(),
    user.totalInvestmentAmount.toLocaleString(),
    user.referralCode || 'N/A',
    user.joinDate,
    user.lastActivity
  ]);

  // Add table
  doc.autoTable({
    head: [tableHeaders],
    body: tableData,
    startY: 70,
    theme: 'grid',
    headStyles: {
      fillColor: primaryColor,
      textColor: [255, 255, 255],
      fontStyle: 'bold',
      fontSize: 9,
      halign: 'center'
    },
    bodyStyles: {
      fontSize: 8,
      cellPadding: 2
    },
    columnStyles: {
      0: { cellWidth: 25, halign: 'left' },   // Name
      1: { cellWidth: 35, halign: 'left' },   // Email
      2: { cellWidth: 20, halign: 'center' }, // Phone
      3: { cellWidth: 15, halign: 'center' }, // Role
      4: { cellWidth: 15, halign: 'center' }, // Status
      5: { cellWidth: 15, halign: 'center' }, // Investments
      6: { cellWidth: 20, halign: 'right' },  // Amount
      7: { cellWidth: 20, halign: 'center' }, // Referral
      8: { cellWidth: 20, halign: 'center' }, // Join Date
      9: { cellWidth: 20, halign: 'center' }  // Last Activity
    },
    alternateRowStyles: {
      fillColor: [248, 250, 252]
    },
    didParseCell: function(data: any) {
      // Color code status
      if (data.column.index === 4) { // Status column
        if (data.cell.text[0] === 'Active') {
          data.cell.styles.textColor = [5, 150, 105]; // Green
          data.cell.styles.fontStyle = 'bold';
        } else if (data.cell.text[0] === 'Inactive') {
          data.cell.styles.textColor = [220, 38, 38]; // Red
          data.cell.styles.fontStyle = 'bold';
        }
      }
      
      // Color code roles
      if (data.column.index === 3) { // Role column
        const role = data.cell.text[0];
        if (role === 'ADMIN') {
          data.cell.styles.textColor = [220, 38, 38]; // Red
          data.cell.styles.fontStyle = 'bold';
        } else if (role === 'INVESTOR') {
          data.cell.styles.textColor = [37, 99, 235]; // Blue
          data.cell.styles.fontStyle = 'bold';
        }
      }
    },
    margin: { left: 20, right: 20 },
    tableWidth: 'auto',
    showHead: 'everyPage'
  });

  // Add footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    
    // Footer background
    doc.setFillColor(245, 245, 245);
    doc.rect(0, 200, 297, 10, 'F');
    
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Golden Miller Financial System | Confidential Member Report', 20, 206);
    doc.text(`Page ${i} of ${pageCount}`, 250, 206);
    doc.text('This report contains sensitive information - Handle according to data protection policies', 20, 209);
  }

  return doc;
};

export const downloadUsersPDF = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  const doc = generateUsersPDF(users, filters);
  const fileName = `golden-miller-members-report-${new Date().toISOString().split('T')[0]}.pdf`;
  doc.save(fileName);
};
