// lib/pdfExport.ts
import jsPDF from 'jspdf';
import 'jspdf-autotable';

// Extend jsPDF type to include autoTable
interface AutoTableStyles {
  fillColor?: number[];
  textColor?: number[];
  fontStyle?: string;
  fontSize?: number;
  halign?: string;
  cellPadding?: number;
}

interface AutoTableOptions {
  head?: string[][];
  body?: string[][];
  startY?: number;
  theme?: string;
  headStyles?: AutoTableStyles;
  bodyStyles?: AutoTableStyles;
  columnStyles?: Record<number, AutoTableStyles & { cellWidth?: number }>;
  alternateRowStyles?: AutoTableStyles;
  didParseCell?: (data: { column: { index: number }; cell: { text: string[]; styles: AutoTableStyles } }) => void;
  margin?: { left?: number; right?: number };
  tableWidth?: string;
  showHead?: string;
}

declare module 'jspdf' {
  interface jsPDF {
    autoTable: (options: AutoTableOptions) => jsPDF;
  }
}

interface UserData {
  name: string;
  email: string;
  phone: string;
  role: string;
  status: string;
  totalInvestments: number;
  totalInvestmentAmount: number;
  referralCode: string;
  bankName: string;
  accountNumber: string;
  accountName: string;
  joinDate: string;
  lastActivity: string;
}

export const generateUsersPDF = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  console.log(`Generating PDF for ${users.length} users`);

  // Create new PDF document in landscape for better table display
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Set document properties
  doc.setProperties({
    title: 'Golden Miller - Investors & Members Report',
    subject: 'Complete Member Database Export',
    author: 'Golden Miller System',
    creator: 'Golden Miller Admin Panel'
  });

  // Colors for Golden Miller branding
  const primaryColor = [234, 179, 8]; // Golden Miller yellow
  const secondaryColor = [31, 41, 55]; // Dark gray
  const lightGray = [249, 250, 251];

  // Calculate statistics
  const totalMembers = users.length;
  const activeMembers = users.filter(u => u.status === 'Active').length;
  const inactiveMembers = totalMembers - activeMembers;
  const totalInvestmentValue = users.reduce((sum, u) => sum + u.totalInvestmentAmount, 0);
  const avgInvestment = totalMembers > 0 ? totalInvestmentValue / totalMembers : 0;
  const investors = users.filter(u => u.role.toLowerCase() === 'investor').length;
  const members = users.filter(u => u.role.toLowerCase() === 'member').length;
  const admins = users.filter(u => u.role.toLowerCase() === 'admin').length;

  // Add header to first page
  doc.setFillColor(...primaryColor);
  doc.rect(0, 0, 297, 30, 'F');

  // Logo/Title
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(22);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 18);

  doc.setFontSize(14);
  doc.setFont('helvetica', 'normal');
  doc.text('Complete Investors & Members Database Report', 20, 25);

  // Date and time
  doc.setFontSize(10);
  const currentDate = new Date().toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
  const currentTime = new Date().toLocaleTimeString('en-US');
  doc.text(`Generated: ${currentDate} at ${currentTime}`, 200, 18);
  doc.text(`Total Records: ${totalMembers}`, 200, 25);

  // Reset text color
  doc.setTextColor(...secondaryColor);

  // Enhanced summary section
  doc.setFillColor(...lightGray);
  doc.rect(20, 40, 257, 35, 'F');
  doc.setDrawColor(200, 200, 200);
  doc.rect(20, 40, 257, 35, 'S');

  doc.setFontSize(16);
  doc.setFont('helvetica', 'bold');
  doc.text('📊 EXECUTIVE SUMMARY', 25, 50);

  doc.setFontSize(11);
  doc.setFont('helvetica', 'normal');

  // Summary stats in organized layout
  doc.text('MEMBERSHIP OVERVIEW:', 25, 60);
  doc.text(`• Total Members: ${totalMembers}`, 30, 65);
  doc.text(`• Active Members: ${activeMembers} (${Math.round((activeMembers/totalMembers)*100)}%)`, 30, 70);
  doc.text(`• Inactive Members: ${inactiveMembers} (${Math.round((inactiveMembers/totalMembers)*100)}%)`, 30, 75);

  doc.text('ROLE DISTRIBUTION:', 150, 60);
  doc.text(`• Investors: ${investors}`, 155, 65);
  doc.text(`• Members: ${members}`, 155, 70);
  doc.text(`• Administrators: ${admins}`, 155, 75);

  doc.text('FINANCIAL SUMMARY:', 25, 85);
  doc.text(`• Total Investment Value: $${totalInvestmentValue.toLocaleString()}`, 30, 90);
  doc.text(`• Average Investment: $${Math.round(avgInvestment).toLocaleString()}`, 30, 95);
  doc.text(`• Active Investors: ${users.filter(u => u.totalInvestments > 0).length}`, 30, 100);

  // Add filters info if any
  if (filters && (filters.search || filters.role !== 'all' || filters.status !== 'all')) {
    doc.setFontSize(9);
    doc.setTextColor(100, 100, 100);
    let filterText = 'APPLIED FILTERS: ';
    if (filters.search) filterText += `Search: "${filters.search}" `;
    if (filters.role && filters.role !== 'all') filterText += `Role: ${filters.role} `;
    if (filters.status && filters.status !== 'all') filterText += `Status: ${filters.status}`;
    doc.text(filterText, 25, 110);
    doc.setTextColor(...secondaryColor);
  }

  // Comprehensive table headers
  const tableHeaders = [
    '#',
    'Full Name',
    'Email Address',
    'Phone Number',
    'Role',
    'Status',
    'Investments',
    'Investment Amount',
    'Referral Code',
    'Bank Details',
    'Join Date',
    'Last Activity'
  ];

  // Prepare comprehensive table data
  const tableData = users.map((user, index) => [
    (index + 1).toString(), // Row number
    user.name || 'N/A',
    user.email || 'N/A',
    user.phone || 'N/A',
    user.role.toUpperCase(),
    user.status,
    user.totalInvestments.toString(),
    `$${user.totalInvestmentAmount.toLocaleString()}`,
    user.referralCode || 'N/A',
    user.bankName !== 'N/A' ? `${user.bankName}\n${user.accountNumber}` : 'N/A',
    user.joinDate,
    user.lastActivity
  ]);

  console.log(`Prepared table data for ${tableData.length} rows`);

  // Add comprehensive table with proper styling
  doc.autoTable({
    head: [tableHeaders],
    body: tableData,
    startY: 120,
    theme: 'grid',
    headStyles: {
      fillColor: primaryColor,
      textColor: [255, 255, 255],
      fontStyle: 'bold',
      fontSize: 8,
      halign: 'center',
      cellPadding: 3
    },
    bodyStyles: {
      fontSize: 7,
      cellPadding: 2,
      lineColor: [200, 200, 200],
      lineWidth: 0.1
    },
    columnStyles: {
      0: { cellWidth: 8, halign: 'center' },   // #
      1: { cellWidth: 30, halign: 'left' },    // Name
      2: { cellWidth: 35, halign: 'left' },    // Email
      3: { cellWidth: 22, halign: 'center' },  // Phone
      4: { cellWidth: 18, halign: 'center' },  // Role
      5: { cellWidth: 15, halign: 'center' },  // Status
      6: { cellWidth: 12, halign: 'center' },  // Investments
      7: { cellWidth: 20, halign: 'right' },   // Amount
      8: { cellWidth: 18, halign: 'center' },  // Referral
      9: { cellWidth: 25, halign: 'left' },    // Bank Details
      10: { cellWidth: 18, halign: 'center' }, // Join Date
      11: { cellWidth: 18, halign: 'center' }  // Last Activity
    },
    alternateRowStyles: {
      fillColor: [248, 250, 252]
    },
    didParseCell: function(data: { column: { index: number }; cell: { text: string[]; styles: AutoTableStyles } }) {
      // Color code status column
      if (data.column.index === 5) { // Status column
        if (data.cell.text[0] === 'Active') {
          data.cell.styles.textColor = [5, 150, 105]; // Green
          data.cell.styles.fontStyle = 'bold';
        } else if (data.cell.text[0] === 'Inactive') {
          data.cell.styles.textColor = [220, 38, 38]; // Red
          data.cell.styles.fontStyle = 'bold';
        }
      }

      // Color code roles
      if (data.column.index === 4) { // Role column
        const role = data.cell.text[0];
        if (role === 'ADMIN') {
          data.cell.styles.textColor = [220, 38, 38]; // Red
          data.cell.styles.fontStyle = 'bold';
        } else if (role === 'INVESTOR') {
          data.cell.styles.textColor = [37, 99, 235]; // Blue
          data.cell.styles.fontStyle = 'bold';
        } else if (role === 'MEMBER') {
          data.cell.styles.textColor = [107, 114, 128]; // Gray
          data.cell.styles.fontStyle = 'bold';
        }
      }

      // Highlight high investment amounts
      if (data.column.index === 7) { // Investment Amount column
        const amount = parseFloat(data.cell.text[0].replace(/[$,]/g, ''));
        if (amount > 10000) {
          data.cell.styles.textColor = [5, 150, 105]; // Green for high amounts
          data.cell.styles.fontStyle = 'bold';
        }
      }
    },
    margin: { left: 15, right: 15 },
    tableWidth: 'auto',
    showHead: 'everyPage',
    pageBreak: 'auto'
  });

  // Add comprehensive footer to all pages
  const pageCount = doc.getNumberOfPages();
  console.log(`PDF generated with ${pageCount} pages`);

  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);

    // Footer background
    doc.setFillColor(245, 245, 245);
    doc.rect(0, 200, 297, 10, 'F');

    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Golden Miller Financial System | Confidential Database Export', 15, 206);
    doc.text(`Page ${i} of ${pageCount} | ${totalMembers} Total Records`, 220, 206);
    doc.text('⚠️ This report contains sensitive financial information - Handle according to data protection policies', 15, 209);
  }

  console.log('PDF generation completed successfully');
  return doc;
};

export const downloadUsersPDF = (users: UserData[], filters?: {
  search?: string;
  role?: string;
  status?: string;
}) => {
  try {
    console.log('Starting PDF generation with users:', users.length);
    const doc = generateUsersPDF(users, filters);
    const fileName = `golden-miller-members-report-${new Date().toISOString().split('T')[0]}.pdf`;
    console.log('PDF generated, attempting to save:', fileName);
    doc.save(fileName);
    console.log('PDF save completed');
  } catch (error) {
    console.error('Error in downloadUsersPDF:', error);
    throw error;
  }
};
