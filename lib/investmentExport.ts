// lib/investmentExport.ts
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';

// Only import autoTable on client side to avoid SSR issues
let autoTable: any = null;
if (typeof window !== 'undefined') {
  import('jspdf-autotable').then(() => {
    console.log('jspdf-autotable loaded successfully for investments');
  }).catch(err => {
    console.error('Failed to load jspdf-autotable for investments:', err);
  });
}

interface InvestmentData {
  _id: string;
  userId: { name: string; email: string };
  plan: { title: string; percentage: number };
  startDate: string;
  endDate: string;
  amount: number;
  status: 'active' | 'inactive' | 'archived';
  duration?: string;
  roi?: number;
}

interface ExportFilters {
  search?: string;
  status?: string;
  plan?: string;
}

// Enhanced PDF generation with comprehensive investment data
export const generateInvestmentsPDF = (investments: InvestmentData[], filters?: ExportFilters) => {
  console.log('Generating comprehensive investment PDF');
  
  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Header
  doc.setFillColor(34, 197, 94);
  doc.rect(0, 0, 297, 25, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 15);
  
  doc.setFontSize(12);
  doc.text('Investment Portfolio Report', 20, 20);

  // Reset text color
  doc.setTextColor(0, 0, 0);
  
  // Enhanced Summary
  doc.setFillColor(249, 250, 251);
  doc.rect(20, 35, 257, 35, 'F');
  doc.setDrawColor(200, 200, 200);
  doc.rect(20, 35, 257, 35, 'S');
  
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('📊 INVESTMENT SUMMARY', 25, 45);
  
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  
  const totalInvestments = investments.length;
  const activeInvestments = investments.filter(inv => inv.status === 'active').length;
  const totalValue = investments.reduce((sum, inv) => sum + inv.amount, 0);
  const averageInvestment = totalValue / totalInvestments || 0;
  const totalROI = investments.reduce((sum, inv) => sum + (inv.roi || 0), 0);
  
  doc.text(`Total Investments: ${totalInvestments}`, 25, 52);
  doc.text(`Active: ${activeInvestments} (${Math.round((activeInvestments/totalInvestments)*100)}%)`, 25, 57);
  doc.text(`Total Portfolio Value: $${totalValue.toLocaleString()}`, 25, 62);
  doc.text(`Average Investment: $${averageInvestment.toLocaleString()}`, 150, 52);
  doc.text(`Total Expected ROI: ${totalROI.toFixed(1)}%`, 150, 57);
  doc.text(`Generated: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 150, 62);

  // Apply filters info
  if (filters?.search || filters?.status !== 'all' || filters?.plan !== 'all') {
    doc.text('Applied Filters:', 25, 67);
    let filterText = '';
    if (filters?.search) filterText += `Search: "${filters.search}" `;
    if (filters?.status !== 'all') filterText += `Status: ${filters.status} `;
    if (filters?.plan !== 'all') filterText += `Plan: ${filters.plan}`;
    doc.text(filterText, 80, 67);
  }

  // Check if we're on client side and autoTable is available
  const hasAutoTable = typeof window !== 'undefined' && (window as any).jsPDF?.API?.autoTable;
  
  if (hasAutoTable) {
    // Table Headers
    const headers = [
      ['#', 'Investor', 'Email', 'Plan', 'Amount', 'ROI %', 'Duration', 'Start Date', 'End Date', 'Status']
    ];

    // Table Data
    const tableData = investments.map((investment, index) => [
      (index + 1).toString(),
      investment.userId?.name || 'N/A',
      investment.userId?.email || 'N/A',
      investment.plan?.title || 'N/A',
      `$${investment.amount.toLocaleString()}`,
      `${investment.roi || investment.plan?.percentage || 0}%`,
      investment.duration || 'N/A',
      new Date(investment.startDate).toLocaleDateString(),
      new Date(investment.endDate).toLocaleDateString(),
      investment.status.toUpperCase()
    ]);

    // Generate table with autoTable
    (doc as any).autoTable({
      head: headers,
      body: tableData,
      startY: 80,
      theme: 'grid',
      styles: {
        fontSize: 8,
        cellPadding: 3,
        overflow: 'linebreak',
        halign: 'left'
      },
      headStyles: {
        fillColor: [34, 197, 94],
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        fontSize: 9
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 15 },
        1: { cellWidth: 35 },
        2: { cellWidth: 40 },
        3: { cellWidth: 25 },
        4: { halign: 'right', cellWidth: 25 },
        5: { halign: 'center', cellWidth: 20 },
        6: { halign: 'center', cellWidth: 25 },
        7: { halign: 'center', cellWidth: 25 },
        8: { halign: 'center', cellWidth: 25 },
        9: { halign: 'center', cellWidth: 25 }
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252]
      },
      didDrawCell: function(data: any) {
        // Color-code status column
        if (data.column.index === 9) {
          const status = data.cell.text[0];
          if (status === 'ACTIVE') {
            doc.setTextColor(5, 150, 105);
          } else if (status === 'INACTIVE') {
            doc.setTextColor(220, 38, 38);
          } else {
            doc.setTextColor(107, 114, 128);
          }
        }
      }
    });
  } else {
    // Fallback to simple table without autoTable
    console.log('AutoTable not available, using simple table layout');
    
    // Table Headers
    let yPos = 85;
    doc.setFillColor(34, 197, 94);
    doc.rect(20, yPos - 5, 257, 10, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    
    const cols = {
      num: 25, name: 45, email: 80, plan: 110, amount: 135, roi: 155, 
      duration: 175, start: 200, end: 225, status: 250
    };
    
    doc.text('#', cols.num, yPos);
    doc.text('Investor', cols.name, yPos);
    doc.text('Email', cols.email, yPos);
    doc.text('Plan', cols.plan, yPos);
    doc.text('Amount', cols.amount, yPos);
    doc.text('ROI%', cols.roi, yPos);
    doc.text('Duration', cols.duration, yPos);
    doc.text('Start', cols.start, yPos);
    doc.text('End', cols.end, yPos);
    doc.text('Status', cols.status, yPos);
    
    yPos += 10;
    doc.setTextColor(0, 0, 0);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(7);
    
    // Table Data
    investments.slice(0, 25).forEach((investment, index) => {
      if (yPos > 190) {
        doc.addPage();
        yPos = 20;
      }
      
      if (index % 2 === 0) {
        doc.setFillColor(248, 250, 252);
        doc.rect(20, yPos - 3, 257, 8, 'F');
      }
      
      doc.text((index + 1).toString(), cols.num, yPos);
      doc.text((investment.userId?.name || 'N/A').substring(0, 15), cols.name, yPos);
      doc.text((investment.userId?.email || 'N/A').substring(0, 18), cols.email, yPos);
      doc.text((investment.plan?.title || 'N/A').substring(0, 12), cols.plan, yPos);
      doc.text(`$${investment.amount.toLocaleString()}`, cols.amount, yPos);
      doc.text(`${investment.roi || investment.plan?.percentage || 0}%`, cols.roi, yPos);
      doc.text((investment.duration || 'N/A').substring(0, 8), cols.duration, yPos);
      doc.text(new Date(investment.startDate).toLocaleDateString().substring(0, 8), cols.start, yPos);
      doc.text(new Date(investment.endDate).toLocaleDateString().substring(0, 8), cols.end, yPos);
      
      // Color-coded status
      if (investment.status === 'active') {
        doc.setTextColor(5, 150, 105);
      } else if (investment.status === 'inactive') {
        doc.setTextColor(220, 38, 38);
      } else {
        doc.setTextColor(107, 114, 128);
      }
      doc.text(investment.status.toUpperCase(), cols.status, yPos);
      doc.setTextColor(0, 0, 0);
      
      yPos += 8;
    });
    
    if (investments.length > 25) {
      yPos += 5;
      doc.text(`... and ${investments.length - 25} more investments`, 20, yPos);
    }
  }

  // Footer on all pages
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    
    doc.setFillColor(245, 245, 245);
    doc.rect(0, 200, 297, 10, 'F');
    
    doc.setFontSize(8);
    doc.setTextColor(100, 100, 100);
    doc.text('Golden Miller Investment System | Portfolio Export', 20, 206);
    doc.text(`Page ${i} of ${pageCount} | ${totalInvestments} Total Investments | $${totalValue.toLocaleString()} Portfolio Value`, 150, 206);
  }

  console.log(`Investment PDF generated with ${pageCount} pages for ${investments.length} investments`);
  return doc;
};

// Simple fallback PDF generation without autoTable
export const generateSimpleInvestmentsPDF = (investments: InvestmentData[], filters?: ExportFilters) => {
  console.log('Generating simple investment PDF without autoTable');
  
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // Header
  doc.setFillColor(34, 197, 94);
  doc.rect(0, 0, 210, 25, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 15);
  
  doc.setFontSize(12);
  doc.text('Investment Portfolio Report', 20, 20);

  // Reset text color
  doc.setTextColor(0, 0, 0);
  
  // Summary
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Summary', 20, 40);
  
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  const totalValue = investments.reduce((sum, inv) => sum + inv.amount, 0);
  doc.text(`Total Investments: ${investments.length}`, 20, 50);
  doc.text(`Active Investments: ${investments.filter(inv => inv.status === 'active').length}`, 20, 55);
  doc.text(`Total Portfolio Value: $${totalValue.toLocaleString()}`, 20, 60);
  doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 65);

  // Investment list
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('Investment Details', 20, 80);
  
  let yPosition = 90;
  doc.setFontSize(9);
  doc.setFont('helvetica', 'normal');
  
  investments.slice(0, 20).forEach((investment, index) => {
    if (yPosition > 270) {
      doc.addPage();
      yPosition = 20;
    }
    
    doc.text(`${index + 1}. ${investment.userId?.name || 'N/A'}`, 20, yPosition);
    doc.text(`Plan: ${investment.plan?.title || 'N/A'}`, 25, yPosition + 4);
    doc.text(`Amount: $${investment.amount.toLocaleString()} | Status: ${investment.status}`, 25, yPosition + 8);
    doc.text(`Duration: ${investment.duration || 'N/A'} | ROI: ${investment.roi || investment.plan?.percentage || 0}%`, 25, yPosition + 12);
    
    yPosition += 20;
  });
  
  if (investments.length > 20) {
    yPosition += 5;
    doc.text(`... and ${investments.length - 20} more investments`, 20, yPosition);
  }

  return doc;
};

export const downloadInvestmentsPDF = (investments: InvestmentData[], filters?: ExportFilters) => {
  try {
    console.log('Starting investment PDF generation with investments:', investments.length);
    
    if (!investments || investments.length === 0) {
      throw new Error('No investment data provided for PDF generation');
    }
    
    let doc;
    
    // Check if we're on client side and autoTable is available
    const hasAutoTable = typeof window !== 'undefined' && (window as any).jsPDF?.API?.autoTable;
    
    if (hasAutoTable) {
      try {
        console.log('AutoTable available, attempting comprehensive PDF...');
        doc = generateInvestmentsPDF(investments, filters);
        console.log('Comprehensive investment PDF generated successfully');
      } catch (autoTableError) {
        console.warn('AutoTable PDF failed, falling back to simple PDF:', autoTableError);
        doc = generateSimpleInvestmentsPDF(investments, filters);
        console.log('Simple investment PDF generated successfully');
      }
    } else {
      console.log('AutoTable not available, using simple PDF...');
      doc = generateSimpleInvestmentsPDF(investments, filters);
      console.log('Simple investment PDF generated successfully');
    }
    
    if (!doc) {
      throw new Error('PDF document generation failed');
    }
    
    const fileName = `golden-miller-investments-report-${new Date().toISOString().split('T')[0]}.pdf`;
    console.log('Investment PDF generated successfully, attempting to save:', fileName);
    
    doc.save(fileName);
    console.log('Investment PDF save completed successfully');
    
  } catch (error) {
    console.error('Error in downloadInvestmentsPDF:', error);
    console.error('Error stack:', (error as Error).stack);
    throw new Error(`Investment PDF export failed: ${(error as Error).message}`);
  }
};

export const downloadInvestmentsExcel = (investments: InvestmentData[], filters?: ExportFilters) => {
  try {
    console.log('Starting investment Excel generation with investments:', investments.length);
    
    if (!investments || investments.length === 0) {
      throw new Error('No investment data provided for Excel generation');
    }

    // Prepare data for Excel
    const excelData = investments.map((investment, index) => ({
      '#': index + 1,
      'Investor Name': investment.userId?.name || 'N/A',
      'Email': investment.userId?.email || 'N/A',
      'Investment Plan': investment.plan?.title || 'N/A',
      'Amount': investment.amount,
      'ROI Percentage': investment.roi || investment.plan?.percentage || 0,
      'Duration': investment.duration || 'N/A',
      'Start Date': new Date(investment.startDate).toLocaleDateString(),
      'End Date': new Date(investment.endDate).toLocaleDateString(),
      'Status': investment.status.toUpperCase(),
      'Plan Percentage': investment.plan?.percentage || 0
    }));

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(excelData);

    // Set column widths
    const colWidths = [
      { wch: 5 },   // #
      { wch: 20 },  // Investor Name
      { wch: 25 },  // Email
      { wch: 15 },  // Investment Plan
      { wch: 12 },  // Amount
      { wch: 10 },  // ROI Percentage
      { wch: 12 },  // Duration
      { wch: 12 },  // Start Date
      { wch: 12 },  // End Date
      { wch: 10 },  // Status
      { wch: 12 }   // Plan Percentage
    ];
    ws['!cols'] = colWidths;

    // Add summary sheet
    const summaryData = [
      { Metric: 'Total Investments', Value: investments.length },
      { Metric: 'Active Investments', Value: investments.filter(inv => inv.status === 'active').length },
      { Metric: 'Inactive Investments', Value: investments.filter(inv => inv.status === 'inactive').length },
      { Metric: 'Archived Investments', Value: investments.filter(inv => inv.status === 'archived').length },
      { Metric: 'Total Portfolio Value', Value: `$${investments.reduce((sum, inv) => sum + inv.amount, 0).toLocaleString()}` },
      { Metric: 'Average Investment', Value: `$${(investments.reduce((sum, inv) => sum + inv.amount, 0) / investments.length || 0).toLocaleString()}` },
      { Metric: 'Generated Date', Value: new Date().toLocaleDateString() },
      { Metric: 'Generated Time', Value: new Date().toLocaleTimeString() }
    ];

    if (filters?.search || filters?.status !== 'all' || filters?.plan !== 'all') {
      summaryData.push({ Metric: 'Filters Applied', Value: 'Yes' });
      if (filters?.search) summaryData.push({ Metric: 'Search Filter', Value: filters.search });
      if (filters?.status !== 'all') summaryData.push({ Metric: 'Status Filter', Value: filters.status });
      if (filters?.plan !== 'all') summaryData.push({ Metric: 'Plan Filter', Value: filters.plan });
    }

    const summaryWs = XLSX.utils.json_to_sheet(summaryData);
    summaryWs['!cols'] = [{ wch: 20 }, { wch: 30 }];

    // Add sheets to workbook
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');
    XLSX.utils.book_append_sheet(wb, ws, 'Investments');

    // Generate filename and save
    const fileName = `golden-miller-investments-report-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);
    
    console.log('Investment Excel export completed successfully');
    
  } catch (error) {
    console.error('Error in downloadInvestmentsExcel:', error);
    throw new Error(`Investment Excel export failed: ${(error as Error).message}`);
  }
};
