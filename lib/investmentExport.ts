// lib/investmentExport.ts
import jsPDF from 'jspdf';
import * as XLSX from 'xlsx';

// Only import autoTable on client side to avoid SSR issues
let autoTable: any = null;
if (typeof window !== 'undefined') {
  import('jspdf-autotable').then((module) => {
    autoTable = module.default;
    console.log('jspdf-autotable loaded successfully for investments');
  }).catch(err => {
    console.error('Failed to load jspdf-autotable for investments:', err);
  });
}

interface InvestmentData {
  _id: string;
  userId: { name: string; email: string };
  plan: { title: string; percentage: number };
  startDate: string;
  endDate: string;
  amount: number;
  status: 'active' | 'inactive' | 'archived';
  duration?: string;
  roi?: number;
}

interface ExportFilters {
  search?: string;
  status?: string;
  plan?: string;
}

// Enhanced PDF generation with comprehensive investment data - ALL RECORDS
export const generateInvestmentsPDF = (investments: InvestmentData[], filters?: ExportFilters) => {
  console.log('Generating comprehensive investment PDF with ALL database records');

  const doc = new jsPDF({
    orientation: 'landscape',
    unit: 'mm',
    format: 'a4'
  });

  // Header
  doc.setFillColor(34, 197, 94);
  doc.rect(0, 0, 297, 25, 'F');

  doc.setTextColor(255, 255, 255);
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 15);

  doc.setFontSize(12);
  doc.text('Complete Investment Database Export', 20, 20);

  // Reset text color
  doc.setTextColor(0, 0, 0);

  // Enhanced Summary
  doc.setFillColor(249, 250, 251);
  doc.rect(20, 35, 257, 35, 'F');
  doc.setDrawColor(200, 200, 200);
  doc.rect(20, 35, 257, 35, 'S');

  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('📊 COMPLETE INVESTMENT DATABASE SUMMARY', 25, 45);

  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');

  const totalInvestments = investments.length;
  const activeInvestments = investments.filter(inv => inv.status === 'active').length;
  const inactiveInvestments = investments.filter(inv => inv.status === 'inactive').length;
  const archivedInvestments = investments.filter(inv => inv.status === 'archived').length;
  const totalValue = investments.reduce((sum, inv) => sum + inv.amount, 0);
  const averageInvestment = totalValue / totalInvestments || 0;
  const averageROI = investments.reduce((sum, inv) => sum + (inv.roi || 0), 0) / totalInvestments || 0;

  doc.text(`Total Investments: ${totalInvestments}`, 25, 52);
  doc.text(`Active: ${activeInvestments} (${Math.round((activeInvestments/totalInvestments)*100)}%)`, 25, 57);
  doc.text(`Inactive: ${inactiveInvestments} | Archived: ${archivedInvestments}`, 25, 62);
  doc.text(`Total Portfolio Value: $${totalValue.toLocaleString()}`, 150, 52);
  doc.text(`Average Investment: $${averageInvestment.toLocaleString()}`, 150, 57);
  doc.text(`Average ROI: ${averageROI.toFixed(1)}%`, 150, 62);
  doc.text(`Generated: ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, 25, 67);

  // Apply filters info
  if (filters?.search || filters?.status !== 'all' || filters?.plan !== 'all') {
    doc.text('Applied Filters:', 150, 67);
    let filterText = '';
    if (filters?.search) filterText += `Search: "${filters.search}" `;
    if (filters?.status !== 'all') filterText += `Status: ${filters.status} `;
    if (filters?.plan !== 'all') filterText += `Plan: ${filters.plan}`;
    doc.text(filterText, 200, 67);
  }

  // Check if we're on client side and autoTable is available
  const hasAutoTable = typeof window !== 'undefined' && (window as any).jsPDF?.API?.autoTable;
  
  if (hasAutoTable) {
    // Comprehensive Table Headers - ALL FIELDS
    const headers = [
      ['#', 'Investor Name', 'Email Address', 'Investment Plan', 'Amount ($)', 'ROI %', 'Duration', 'Start Date', 'End Date', 'Status', 'Created Date']
    ];

    // Comprehensive Table Data - ALL RECORDS
    const tableData = investments.map((investment, index) => [
      (index + 1).toString(),
      investment.userId?.name || 'N/A',
      investment.userId?.email || 'N/A',
      investment.plan?.title || 'N/A',
      `$${investment.amount.toLocaleString()}`,
      `${investment.roi || investment.plan?.percentage || 0}%`,
      investment.duration || 'N/A',
      new Date(investment.startDate).toLocaleDateString(),
      new Date(investment.endDate).toLocaleDateString(),
      investment.status.toUpperCase(),
      investment.createdAt ? new Date(investment.createdAt).toLocaleDateString() : 'N/A'
    ]);

    console.log(`Generating PDF table with ${tableData.length} rows of data`);

    // Generate comprehensive table with autoTable - ALL RECORDS WITH PROPER PAGINATION
    (doc as any).autoTable({
      head: headers,
      body: tableData,
      startY: 80,
      theme: 'grid',
      styles: {
        fontSize: 6,
        cellPadding: 1.5,
        overflow: 'linebreak',
        halign: 'left',
        valign: 'middle'
      },
      headStyles: {
        fillColor: [34, 197, 94],
        textColor: [255, 255, 255],
        fontStyle: 'bold',
        fontSize: 7,
        halign: 'center'
      },
      columnStyles: {
        0: { halign: 'center', cellWidth: 10 },  // #
        1: { cellWidth: 28 },                    // Investor Name
        2: { cellWidth: 32 },                    // Email
        3: { cellWidth: 22 },                    // Plan
        4: { halign: 'right', cellWidth: 20 },   // Amount
        5: { halign: 'center', cellWidth: 12 },  // ROI %
        6: { halign: 'center', cellWidth: 18 },  // Duration
        7: { halign: 'center', cellWidth: 20 },  // Start Date
        8: { halign: 'center', cellWidth: 20 },  // End Date
        9: { halign: 'center', cellWidth: 18 },  // Status
        10: { halign: 'center', cellWidth: 20 }  // Created Date
      },
      alternateRowStyles: {
        fillColor: [248, 250, 252]
      },
      margin: { top: 20, left: 8, right: 8, bottom: 20 },
      pageBreak: 'auto',
      showHead: 'everyPage',
      tableWidth: 'auto',

      // Enhanced pagination settings
      didDrawPage: function(data: any) {
        console.log(`Drawing page ${data.pageNumber} with ${data.table.body.length} rows`);

        // Add page header on every page
        doc.setFillColor(34, 197, 94);
        doc.rect(0, 0, 297, 15, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'bold');
        doc.text('🌾 GOLDEN MILLER - Complete Investment Database', 10, 10);

        // Add page footer
        doc.setFillColor(245, 245, 245);
        doc.rect(0, 200, 297, 10, 'F');
        doc.setFontSize(8);
        doc.setTextColor(100, 100, 100);
        doc.text(`Page ${data.pageNumber} | ${investments.length} Total Investments`, 10, 206);
        doc.text(`Generated: ${new Date().toLocaleDateString()}`, 200, 206);
      },

      didDrawCell: function(data: any) {
        // Color-code status column (index 9)
        if (data.column.index === 9) {
          const status = data.cell.text[0];
          if (status === 'ACTIVE') {
            doc.setTextColor(5, 150, 105);
          } else if (status === 'INACTIVE') {
            doc.setTextColor(220, 38, 38);
          } else {
            doc.setTextColor(107, 114, 128);
          }
        }
      }
    });

    console.log(`PDF table generation completed. Total pages: ${doc.getNumberOfPages()}`);
    console.log(`Successfully included all ${tableData.length} investment records`);
  } else {
    // Fallback to simple table without autoTable
    console.log('AutoTable not available, using simple table layout');
    
    // Table Headers
    let yPos = 85;
    doc.setFillColor(34, 197, 94);
    doc.rect(20, yPos - 5, 257, 10, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'bold');
    
    const cols = {
      num: 25, name: 45, email: 80, plan: 110, amount: 135, roi: 155, 
      duration: 175, start: 200, end: 225, status: 250
    };
    
    doc.text('#', cols.num, yPos);
    doc.text('Investor', cols.name, yPos);
    doc.text('Email', cols.email, yPos);
    doc.text('Plan', cols.plan, yPos);
    doc.text('Amount', cols.amount, yPos);
    doc.text('ROI%', cols.roi, yPos);
    doc.text('Duration', cols.duration, yPos);
    doc.text('Start', cols.start, yPos);
    doc.text('End', cols.end, yPos);
    doc.text('Status', cols.status, yPos);
    
    yPos += 10;
    doc.setTextColor(0, 0, 0);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(7);
    
    // Table Data - ALL RECORDS with proper pagination
    console.log(`Simple PDF: Processing all ${investments.length} investments`);

    investments.forEach((investment, index) => {
      // Check if we need a new page
      if (yPos > 190) {
        doc.addPage();
        yPos = 30; // Start position on new page

        // Add header on new page
        doc.setFillColor(34, 197, 94);
        doc.rect(0, 0, 210, 20, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('🌾 GOLDEN MILLER - Investment Database (Continued)', 10, 12);
        doc.setTextColor(0, 0, 0);

        // Re-add table headers
        yPos = 25;
        doc.setFillColor(34, 197, 94);
        doc.rect(20, yPos - 5, 170, 10, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(8);
        doc.setFont('helvetica', 'bold');
        doc.text('#', cols.num, yPos);
        doc.text('Investor', cols.name, yPos);
        doc.text('Email', cols.email, yPos);
        doc.text('Plan', cols.plan, yPos);
        doc.text('Amount', cols.amount, yPos);
        doc.text('ROI%', cols.roi, yPos);
        doc.text('Duration', cols.duration, yPos);
        doc.text('Start', cols.start, yPos);
        doc.text('End', cols.end, yPos);
        doc.text('Status', cols.status, yPos);
        yPos += 10;
        doc.setTextColor(0, 0, 0);
        doc.setFont('helvetica', 'normal');
        doc.setFontSize(7);
      }

      // Alternate row colors
      if (index % 2 === 0) {
        doc.setFillColor(248, 250, 252);
        doc.rect(20, yPos - 3, 170, 8, 'F');
      }

      // Add row data
      doc.text((index + 1).toString(), cols.num, yPos);
      doc.text((investment.userId?.name || 'N/A').substring(0, 12), cols.name, yPos);
      doc.text((investment.userId?.email || 'N/A').substring(0, 15), cols.email, yPos);
      doc.text((investment.plan?.title || 'N/A').substring(0, 10), cols.plan, yPos);
      doc.text(`$${investment.amount.toLocaleString()}`, cols.amount, yPos);
      doc.text(`${investment.roi || investment.plan?.percentage || 0}%`, cols.roi, yPos);
      doc.text((investment.duration || 'N/A').substring(0, 6), cols.duration, yPos);
      doc.text(new Date(investment.startDate).toLocaleDateString().substring(0, 8), cols.start, yPos);
      doc.text(new Date(investment.endDate).toLocaleDateString().substring(0, 8), cols.end, yPos);

      // Color-coded status
      if (investment.status === 'active') {
        doc.setTextColor(5, 150, 105);
      } else if (investment.status === 'inactive') {
        doc.setTextColor(220, 38, 38);
      } else {
        doc.setTextColor(107, 114, 128);
      }
      doc.text(investment.status.toUpperCase(), cols.status, yPos);
      doc.setTextColor(0, 0, 0);

      yPos += 7;
    });

    console.log(`Simple PDF: Successfully processed all ${investments.length} investments across ${doc.getNumberOfPages()} pages`);
  }

  // Final page count and summary
  const pageCount = doc.getNumberOfPages();
  console.log(`✅ PDF GENERATION COMPLETE:`);
  console.log(`   - Total Pages: ${pageCount}`);
  console.log(`   - Total Investments: ${investments.length}`);
  console.log(`   - Records per page: ~${Math.ceil(investments.length / pageCount)}`);
  console.log(`   - All records included: ${investments.length === tableData.length ? 'YES' : 'NO'}`);

  return doc;
};

// Simple fallback PDF generation without autoTable
export const generateSimpleInvestmentsPDF = (investments: InvestmentData[], filters?: ExportFilters) => {
  console.log('Generating simple investment PDF without autoTable');
  
  const doc = new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4'
  });

  // Header
  doc.setFillColor(34, 197, 94);
  doc.rect(0, 0, 210, 25, 'F');
  
  doc.setTextColor(255, 255, 255);
  doc.setFontSize(18);
  doc.setFont('helvetica', 'bold');
  doc.text('🌾 GOLDEN MILLER', 20, 15);
  
  doc.setFontSize(12);
  doc.text('Investment Portfolio Report', 20, 20);

  // Reset text color
  doc.setTextColor(0, 0, 0);
  
  // Summary
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text('Summary', 20, 40);
  
  doc.setFontSize(10);
  doc.setFont('helvetica', 'normal');
  const totalValue = investments.reduce((sum, inv) => sum + inv.amount, 0);
  doc.text(`Total Investments: ${investments.length}`, 20, 50);
  doc.text(`Active Investments: ${investments.filter(inv => inv.status === 'active').length}`, 20, 55);
  doc.text(`Total Portfolio Value: $${totalValue.toLocaleString()}`, 20, 60);
  doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, 65);

  // Investment list - ALL RECORDS
  doc.setFontSize(12);
  doc.setFont('helvetica', 'bold');
  doc.text('Complete Investment Details', 20, 80);

  let yPosition = 90;
  doc.setFontSize(8);
  doc.setFont('helvetica', 'normal');

  console.log(`Simple PDF list: Processing all ${investments.length} investments`);

  investments.forEach((investment, index) => {
    if (yPosition > 270) {
      doc.addPage();
      yPosition = 20;

      // Add header on new page
      doc.setFillColor(34, 197, 94);
      doc.rect(0, 0, 210, 15, 'F');
      doc.setTextColor(255, 255, 255);
      doc.setFontSize(10);
      doc.setFont('helvetica', 'bold');
      doc.text('🌾 GOLDEN MILLER - Investment Details (Continued)', 10, 10);
      doc.setTextColor(0, 0, 0);
      doc.setFontSize(8);
      doc.setFont('helvetica', 'normal');
      yPosition = 25;
    }

    doc.text(`${index + 1}. ${investment.userId?.name || 'N/A'}`, 20, yPosition);
    doc.text(`Email: ${investment.userId?.email || 'N/A'}`, 25, yPosition + 4);
    doc.text(`Plan: ${investment.plan?.title || 'N/A'} | ROI: ${investment.roi || investment.plan?.percentage || 0}%`, 25, yPosition + 8);
    doc.text(`Amount: $${investment.amount.toLocaleString()} | Status: ${investment.status.toUpperCase()}`, 25, yPosition + 12);
    doc.text(`Duration: ${investment.duration || 'N/A'} | Start: ${new Date(investment.startDate).toLocaleDateString()}`, 25, yPosition + 16);

    yPosition += 22;
  });

  console.log(`Simple PDF list: Successfully processed all ${investments.length} investments across ${doc.getNumberOfPages()} pages`);

  return doc;
};

export const downloadInvestmentsPDF = (investments: InvestmentData[], filters?: ExportFilters) => {
  try {
    console.log('Starting comprehensive investment PDF generation with ALL database records:', investments.length);

    if (!investments || investments.length === 0) {
      throw new Error('No investment data provided for PDF generation');
    }

    let doc;

    // Force table format - check multiple ways for autoTable availability
    const hasAutoTable = typeof window !== 'undefined' && (
      (window as any).jsPDF?.API?.autoTable ||
      autoTable ||
      typeof (window as any).autoTable !== 'undefined'
    );

    console.log('AutoTable availability check:', {
      hasWindow: typeof window !== 'undefined',
      hasJsPDFAutoTable: !!(window as any).jsPDF?.API?.autoTable,
      hasAutoTableVar: !!autoTable,
      hasGlobalAutoTable: typeof (window as any).autoTable !== 'undefined'
    });

    if (hasAutoTable) {
      try {
        console.log('AutoTable available, generating comprehensive table PDF with ALL records...');
        doc = generateInvestmentsPDF(investments, filters);
        console.log('Comprehensive investment table PDF generated successfully');
      } catch (autoTableError) {
        console.warn('AutoTable PDF failed, falling back to simple table PDF:', autoTableError);
        doc = generateSimpleInvestmentsPDF(investments, filters);
        console.log('Simple investment table PDF generated successfully');
      }
    } else {
      console.log('AutoTable not available, using simple table PDF...');
      doc = generateSimpleInvestmentsPDF(investments, filters);
      console.log('Simple investment table PDF generated successfully');
    }

    if (!doc) {
      throw new Error('PDF document generation failed');
    }

    const fileName = `golden-miller-complete-investments-database-${new Date().toISOString().split('T')[0]}.pdf`;
    console.log('Investment PDF with ALL database records generated successfully, saving:', fileName);

    doc.save(fileName);
    console.log('Complete investment database PDF save completed successfully');

  } catch (error) {
    console.error('Error in downloadInvestmentsPDF:', error);
    console.error('Error stack:', (error as Error).stack);
    throw new Error(`Investment PDF export failed: ${(error as Error).message}`);
  }
};

export const downloadInvestmentsExcel = (investments: InvestmentData[], filters?: ExportFilters) => {
  try {
    console.log('Starting comprehensive investment Excel generation with ALL database records:', investments.length);

    if (!investments || investments.length === 0) {
      throw new Error('No investment data provided for Excel generation');
    }

    // Prepare comprehensive data for Excel - ALL FIELDS
    const excelData = investments.map((investment, index) => ({
      '#': index + 1,
      'Investor Name': investment.userId?.name || 'N/A',
      'Email Address': investment.userId?.email || 'N/A',
      'Investment Plan': investment.plan?.title || 'N/A',
      'Investment Amount ($)': investment.amount,
      'ROI Percentage (%)': investment.roi || investment.plan?.percentage || 0,
      'Plan Base ROI (%)': investment.plan?.percentage || 0,
      'Duration': investment.duration || 'N/A',
      'Start Date': new Date(investment.startDate).toLocaleDateString(),
      'End Date': new Date(investment.endDate).toLocaleDateString(),
      'Status': investment.status.toUpperCase(),
      'Expected Return ($)': Math.round(investment.amount * ((investment.roi || investment.plan?.percentage || 0) / 100)),
      'Total Value ($)': Math.round(investment.amount * (1 + ((investment.roi || investment.plan?.percentage || 0) / 100))),
      'Created Date': investment.createdAt ? new Date(investment.createdAt).toLocaleDateString() : 'N/A',
      'Last Updated': investment.updatedAt ? new Date(investment.updatedAt).toLocaleDateString() : 'N/A'
    }));

    // Create workbook and worksheet
    const wb = XLSX.utils.book_new();
    const ws = XLSX.utils.json_to_sheet(excelData);

    // Set comprehensive column widths for ALL FIELDS
    const colWidths = [
      { wch: 5 },   // #
      { wch: 20 },  // Investor Name
      { wch: 30 },  // Email Address
      { wch: 18 },  // Investment Plan
      { wch: 15 },  // Investment Amount
      { wch: 12 },  // ROI Percentage
      { wch: 12 },  // Plan Base ROI
      { wch: 12 },  // Duration
      { wch: 12 },  // Start Date
      { wch: 12 },  // End Date
      { wch: 10 },  // Status
      { wch: 15 },  // Expected Return
      { wch: 15 },  // Total Value
      { wch: 12 },  // Created Date
      { wch: 12 }   // Last Updated
    ];
    ws['!cols'] = colWidths;

    // Add comprehensive summary sheet with ALL DATABASE STATISTICS
    const totalValue = investments.reduce((sum, inv) => sum + inv.amount, 0);
    const totalExpectedReturns = investments.reduce((sum, inv) => sum + (inv.amount * ((inv.roi || inv.plan?.percentage || 0) / 100)), 0);
    const totalPortfolioValue = totalValue + totalExpectedReturns;
    const averageROI = investments.reduce((sum, inv) => sum + (inv.roi || inv.plan?.percentage || 0), 0) / investments.length || 0;

    const summaryData = [
      { Metric: 'COMPLETE DATABASE EXPORT', Value: 'ALL RECORDS INCLUDED' },
      { Metric: '', Value: '' },
      { Metric: 'Total Investments', Value: investments.length },
      { Metric: 'Active Investments', Value: investments.filter(inv => inv.status === 'active').length },
      { Metric: 'Inactive Investments', Value: investments.filter(inv => inv.status === 'inactive').length },
      { Metric: 'Archived Investments', Value: investments.filter(inv => inv.status === 'archived').length },
      { Metric: '', Value: '' },
      { Metric: 'Total Investment Amount', Value: `$${totalValue.toLocaleString()}` },
      { Metric: 'Total Expected Returns', Value: `$${totalExpectedReturns.toLocaleString()}` },
      { Metric: 'Total Portfolio Value (with ROI)', Value: `$${totalPortfolioValue.toLocaleString()}` },
      { Metric: 'Average Investment Amount', Value: `$${(totalValue / investments.length || 0).toLocaleString()}` },
      { Metric: 'Average ROI Percentage', Value: `${averageROI.toFixed(2)}%` },
      { Metric: '', Value: '' },
      { Metric: 'Export Generated Date', Value: new Date().toLocaleDateString() },
      { Metric: 'Export Generated Time', Value: new Date().toLocaleTimeString() },
      { Metric: 'Export Type', Value: 'Complete Database Export' }
    ];

    if (filters?.search || filters?.status !== 'all' || filters?.plan !== 'all') {
      summaryData.push({ Metric: '', Value: '' });
      summaryData.push({ Metric: 'APPLIED FILTERS', Value: '' });
      if (filters?.search) summaryData.push({ Metric: 'Search Filter', Value: filters.search });
      if (filters?.status !== 'all') summaryData.push({ Metric: 'Status Filter', Value: filters.status });
      if (filters?.plan !== 'all') summaryData.push({ Metric: 'Plan Filter', Value: filters.plan });
    }

    const summaryWs = XLSX.utils.json_to_sheet(summaryData);
    summaryWs['!cols'] = [{ wch: 20 }, { wch: 30 }];

    // Add sheets to workbook with descriptive names
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Executive Summary');
    XLSX.utils.book_append_sheet(wb, ws, 'Complete Investment Database');

    // Generate filename and save
    const fileName = `golden-miller-complete-investments-database-${new Date().toISOString().split('T')[0]}.xlsx`;
    XLSX.writeFile(wb, fileName);

    console.log('Complete investment database Excel export completed successfully');
    
  } catch (error) {
    console.error('Error in downloadInvestmentsExcel:', error);
    throw new Error(`Investment Excel export failed: ${(error as Error).message}`);
  }
};
