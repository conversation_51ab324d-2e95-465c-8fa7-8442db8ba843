

// context/AuthContext.tsx

'use client';

import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { useRouter } from 'next/navigation';
import { User } from '@/types/user';
import {
  loginUser,
  logoutUser,
  getLoggedInUser,
} from '@/lib/frontendAuth';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string, rememberMe: boolean) => Promise<void>;
  logout: () => Promise<void>;
  loading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Attempt to fetch the logged-in user on mount, in case cookies are still valid
  useEffect(() => {
    const checkLoggedIn = async () => {
      try {
        const existingUser = await getLoggedInUser();
        if (existingUser) {
          setUser(existingUser);
        } else {
          setUser(null);
        }
      } catch (error) {
        console.error('Error fetching logged-in user:', error);
        setUser(null);
      } finally {
        setLoading(false);
      }
    };

    checkLoggedIn();
  }, []);

  /**
   * Log in using credentials. The server sets cookies in the response.
   * We then store the returned user in local state.
   */
  const login = async (email: string, password: string, rememberMe: boolean) => {
    try {
      setLoading(true);
      const { user } = await loginUser(email, password, rememberMe);
      setUser(user); 
      console.log('Login successful');
    } catch (error) {
      console.error('Login failed:', error);
      throw error; // Rethrow so UI can display error
    } finally {
      setLoading(false);
    }
  };

  /**
   * Log out the user by calling the server's /api/auth/logout.
   * Then clear user from state and redirect to homepage.
   */
  const logout = async () => {
    try {
      setLoading(true);
      await logoutUser(user?._id || null);
      setUser(null);
      router.push('/');
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if the server fails, we clear local state
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        login,
        logout,
        loading,
      }}
    >
      {loading ? <div>Loading...</div> : children}
    </AuthContext.Provider>
  );
};


