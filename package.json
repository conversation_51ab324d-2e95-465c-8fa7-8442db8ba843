{"name": "<PERSON><PERSON>er", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "migrate": "ts-node --project tsconfig.server.json scripts/migrations/01_add_remember_me_and_convert_refresh_token.ts"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.6", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "busboy": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cors": "^2.8.5", "date-fns": "^4.1.0", "express-rate-limit": "^7.4.0", "form-data": "^4.0.1", "formidable": "^2.0.1", "framer-motion": "^11.5.4", "googleapis": "^144.0.0", "gridfs-stream": "^1.1.1", "ioredis": "^5.4.1", "joi": "^17.13.3", "jose": "^5.9.6", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lottie-react": "^2.4.0", "lucide-react": "^0.439.0", "mailgun.js": "^10.2.4", "mongodb": "^6.9.0", "mongoose": "^8.7.3", "multer": "^1.4.4", "multer-gridfs-storage": "^5.0.2", "next": "14.2.8", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.16", "openai": "^4.68.4", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.2", "react-icons": "^5.3.0", "react-intersection-observer": "^9.13.1", "react-medium-image-zoom": "^5.2.11", "recharts": "^2.15.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "ts-node": "^10.9.1", "uuid": "^10.0.0", "winston": "^3.17.0", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/busboy": "^1.5.4", "@types/cors": "^2.8.17", "@types/formidable": "^3.4.5", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.16", "@types/react": "^18", "@types/react-dom": "^18", "@types/uuid": "^10.0.0", "eslint": "^8", "eslint-config-next": "14.2.8", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}