// // app/(admin)/all-referrals/page.tsx
// 'use client';

// import AdminReferrals from '@/components/admin/referrals/AdminReferrals';
// // import { ReferralLink } from '@/components/admin/referrals/ReferralDashboard';
// // import ReferralDashboard from '@/components/admin/referrals/ReferralDashboard';
// import React from 'react';

// export default function AllReferrals() {
//   return (
//     <> 
//       <div className="bg-white p-6 rounded-lg shadow-md">
//         <h1 className="text-2xl font-bold mb-4">All Client Referrals</h1>
//         <p>Manage All user Referrals.</p>
//     </div>
//      {/* <ReferralDashboard/> */}
//      <AdminReferrals/>
//      {/* <ReferralLink/> */}
//     </>
    
//   );
// }



'use client';

import ReferralStats from '@/components/admin/ReferralStats';
import AdminReferrals from '@/components/admin/referrals/AdminReferrals';
import React from 'react';

export default function AllReferrals() {
  return (
    <div className="space-y-4">
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">All Client Referrals</h1>
        <p className="text-gray-600 dark:text-gray-300">Manage and analyze all user referrals.</p>
      </div>

      <ReferralStats />

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-bold mb-4">Referral Details</h2>
        <AdminReferrals />
      </div>
    </div>
  );
}

