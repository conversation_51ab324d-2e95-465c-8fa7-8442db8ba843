// // app/(admin)/all-investments/page.tsx
// 'use client';

// import SubscriptionTable from '@/components/admin/SubscriptionTable';
// import React from 'react';

// export default function AllInvestments() {
//   return (
    
//     <> 
//     {/* <div className="bg-white p-6 rounded-lg shadow-md">
//       <h1 className="text-2xl font-bold mb-4">All Investments</h1>
     
//     </div> */}

//      <SubscriptionTable/>
//     </>
//   );
// }



'use client';

import InvestmentStats from '@/components/admin/InvestmentStats';
import SubscriptionTable from '@/components/admin/SubscriptionTable';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { InvestmentManagementSkeleton } from '@/components/ui/AdminSkeleton';
import React, { useState, useEffect };

export default function AllInvestments() {
  const [isLoading, setIsLoading] = useState(true);
  const [investmentData, setInvestmentData] = useState(null);

  useEffect(() => {
    const loadInvestmentData = async () => {
      // Simulate loading investment data
      await new Promise(resolve => setTimeout(resolve, 2200));
      setInvestmentData({}); // Set actual data here
      setIsLoading(false);
    };

    loadInvestmentData();
  }, []);

  if (isLoading) {
    return <InvestmentManagementSkeleton />;
  }

  return (
    <div className="space-y-6">
      <LoadingWrapper
        isLoading={false}
        className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4">Investment Overview</h1>
          <p className="text-gray-600 dark:text-gray-300">
            Track and analyze all investment activities across the platform.
          </p>
        </div>
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading investment statistics..."
        loadingVariant="skeleton"
      >
        <InvestmentStats />
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={false}
        loadingText="Loading investment details..."
        loadingVariant="skeleton"
        className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md"
      >
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-bold mb-4">Investment Details</h2>
          <SubscriptionTable />
        </div>
      </LoadingWrapper>
    </div>
  );
}

