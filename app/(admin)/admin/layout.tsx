

// // app/(admin)/admin/layout.tsx
// 'use client';

// import React, { useEffect } from 'react';
// import AdminSidebar from '@/components/navigation/AdminSidebar';
// import AdminTopNavbar from '@/components/navigation/AdminTopNavbar';
// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/navigation';


// export default function AdminLayout({ children }: { children: React.ReactNode }) {
//   const { user, isAuthenticated } = useAuth();
//   const router = useRouter();

//   useEffect(() => {
//     if (user === null) {
//       router.push('/');
//     } else if (user.role !== 'admin') {
//       router.push('/dashboard');
//     }
//   }, [user, router]);

//   if (!isAuthenticated || user?.role !== 'admin') {
//     return <div>Loading...</div>; 
//   }

//   return (
//     <div className="flex h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-200 transition-colors">
//       <AdminSidebar />
//       <div className="flex-1 flex flex-col">
//         <AdminTopNavbar />
//         <main className="flex-1 overflow-auto p-6">{children}</main>
//       </div>
//     </div>
//   );
// }


// // app/(admin)/admin/layout.tsx
// 'use client';

// import React, { useEffect } from 'react';
// import AdminSidebar from '@/components/navigation/AdminSidebar';
// import AdminTopNavbar from '@/components/navigation/AdminTopNavbar';
// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/navigation';

// export default function AdminLayout({ children }: { children: React.ReactNode }) {
//   const { user, isAuthenticated } = useAuth();
//   const router = useRouter();

//   useEffect(() => {
//     if (!isAuthenticated) {
//       router.push('/');
//     } else if (user?.role !== 'admin') {
//       router.push('/dashboard');
//     }
//   }, [isAuthenticated, user, router]);

//   if (!isAuthenticated || user?.role !== 'admin') {
//     return <div>Loading...</div>; 
//   }

//   return (
//     <div className="flex h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-200 transition-colors">
//       <AdminSidebar />
//       <div className="flex-1 flex flex-col">
//         <AdminTopNavbar />
//         <main className="flex-1 overflow-auto p-6">{children}</main>
//       </div>
//     </div>
//   );
// }




// 'use client';

// import React, { useEffect, useState } from 'react';
// import AdminSidebar from '@/components/navigation/AdminSidebar';
// import AdminTopNavbar from '@/components/navigation/AdminTopNavbar';
// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/navigation';

// export default function AdminLayout({ children }: { children: React.ReactNode }) {
//   const { user, isAuthenticated } = useAuth();
//   const router = useRouter();
//   const [isSidebarOpen, setIsSidebarOpen] = useState(false);

//   useEffect(() => {
//     if (!isAuthenticated) {
//       router.push('/');
//     } else if (user?.role !== 'admin') {
//       router.push('/dashboard');
//     }
//   }, [isAuthenticated, user, router]);

//   if (!isAuthenticated || user?.role !== 'admin') {
//     return <div>Loading...</div>; 
//   }

//   const toggleSidebar = () => {
//     setIsSidebarOpen(!isSidebarOpen);
//   };

//   return (
//     <div className="flex h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-200 transition-colors">
//       <AdminSidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
//       <div className="flex-1 flex flex-col md:ml-64">
//         <AdminTopNavbar toggleSidebar={toggleSidebar} />
//         <main className="flex-1 overflow-auto p-6">{children}</main>
//       </div>
//     </div>
//   );
// }




'use client';

import React, { useState } from 'react';
import AdminSidebar from '@/components/navigation/AdminSidebar';
import AdminTopNavbar from '@/components/navigation/AdminTopNavbar';
import { useAuth } from '@/context/AuthContext';
import Loading from '@/components/ui/loading';
import { Toaster } from 'react-hot-toast';

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const { user, isAuthenticated, loading } = useAuth();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  // Toggle the sidebar for smaller screens
  const toggleSidebar = () => {
    setIsSidebarOpen((prev) => !prev);
  };

  // If AuthContext is still fetching user data, show a loader
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading
          variant="page"
          color="primary"
          text="Loading admin panel..."
        />
      </div>
    );
  }

  // Because the middleware already enforces /admin routes for admin users only,
  // by the time we get here, user should be authenticated and have role = 'admin'.
  // If for some reason user is missing or not an admin (edge case),
  // you could optionally show a fallback or message instead.
  if (!isAuthenticated || user?.role !== 'admin') {
    // The user likely got here by a race condition or dev environment mismatch
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading
          variant="page"
          color="secondary"
          text="Checking admin privileges..."
        />
      </div>
    );
  }

  // Render the admin layout once we confirm user is loaded and is an admin
  return (
    <div className="flex h-screen bg-gray-100 dark:bg-gray-900 text-gray-900 dark:text-gray-200 transition-colors">
      <AdminSidebar isOpen={isSidebarOpen} toggleSidebar={toggleSidebar} />
      <div className="flex-1 flex flex-col md:ml-64">
        <AdminTopNavbar toggleSidebar={toggleSidebar} />
        <main className="flex-1 overflow-auto p-6">{children}</main>
      </div>
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#4ade80',
              secondary: '#fff',
            },
          },
          error: {
            duration: 4000,
            iconTheme: {
              primary: '#ef4444',
              secondary: '#fff',
            },
          },
        }}
      />
    </div>
  );
}
