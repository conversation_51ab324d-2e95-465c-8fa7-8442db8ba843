// app/(admin)/admin/all-investors/page.tsx
'use client';

import React, { useState, useEffect } from 'react';
import UserTable from '@/components/admin/UserTable';
import InvestorStats from '@/components/InvestorStats';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { UserManagementSkeleton } from '@/components/ui/AdminSkeleton';
import { Activity } from 'lucide-react';
import PDFTestButton from '@/components/admin/PDFTestButton';

const AllInvestorsPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [, setUserData] = useState(null);

  useEffect(() => {
    const loadUserData = async () => {
      // Simulate loading user management data
      await new Promise(resolve => setTimeout(resolve, 1800));
      setUserData({}); // Set actual data here
      setIsLoading(false);
    };

    loadUserData();
  }, []);

  if (isLoading) {
    return <UserManagementSkeleton />;
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Page Header */}
      <LoadingWrapper
        isLoading={false}
        className="flex items-center justify-between"
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Member Management</h1>
            <p className="text-muted-foreground">
              Manage and monitor all members/investors in the Golden Miller platform
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Activity className="h-5 w-5 text-green-500" />
            <span className="text-sm text-muted-foreground">Live Data</span>
          </div>
        </div>
      </LoadingWrapper>

      {/* PDF Test */}
      <PDFTestButton />

      {/* Statistics Overview */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading member statistics..."
        loadingVariant="skeleton"
      >
        <InvestorStats />
      </LoadingWrapper>

      {/* Member Management Table */}
      <LoadingWrapper
        isLoading={false}
        loadingText="Loading member data..."
        loadingVariant="skeleton"
      >
        <UserTable />
      </LoadingWrapper>
    </div>
  );
};

export default AllInvestorsPage;

