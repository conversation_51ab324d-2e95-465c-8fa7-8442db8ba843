// app/(admin)/admin/all-investors/page.tsx
'use client';

import React from 'react';
import UserTable from '@/components/admin/UserTable';
import InvestorStats from '@/components/InvestorStats';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, TrendingUp, Activity } from 'lucide-react';

const AllInvestorsPage: React.FC = () => {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Member Management</h1>
          <p className="text-muted-foreground">
            Manage and monitor all members/investors in the Golden Miller platform
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Activity className="h-5 w-5 text-green-500" />
          <span className="text-sm text-muted-foreground">Live Data</span>
        </div>
      </div>

      {/* Statistics Overview */}
      <InvestorStats />

      {/* Member Management Table */}
      <UserTable />
    </div>
  );
};

export default AllInvestorsPage;

