// import { NextRequest, NextResponse } from 'next/server';
// import jwt from 'jsonwebtoken';
// import { connectToDatabase } from '@/lib/dbconnect';
// import { Referral } from '@/models/Referral';
// // import { User } from '@/models/User';

// const corsHeaders = {
//   'Access-Control-Allow-Origin': '*', // Adjust for production
//   'Access-Control-Allow-Methods': 'GET, OPTIONS',
//   'Access-Control-Allow-Headers': 'Content-Type, Authorization',
// };

// export async function GET(req: NextRequest) {
//   // Handle preflight OPTIONS request
//   if (req.method === 'OPTIONS') {
//     return NextResponse.json(null, { headers: corsHeaders });
//   }

//   // Extract Authorization header
//   const authHeader = req.headers.get('authorization');
//   if (!authHeader) {
//     return NextResponse.json(
//       { error: 'Authorization header missing' },
//       { status: 401, headers: corsHeaders }
//     );
//   }

//   const token = authHeader.split(' ')[1];
//   if (!token) {
//     return NextResponse.json(
//       { error: 'Authorization token missing' },
//       { status: 401, headers: corsHeaders }
//     );
//   }

//   try {
//     // Verify JWT token
//     const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { id: string; role: string };

//     if (!decoded || decoded.role !== 'admin') {
//       return NextResponse.json(
//         { error: 'Unauthorized access. Admin role required.' },
//         { status: 403, headers: corsHeaders }
//       );
//     }

//     // Connect to database
//     await connectToDatabase();

//     // Fetch all referrals with related referrer and referred user information
//     const allReferrals = await Referral.find({})
//       .populate('referrerId', 'name email') // Include referrer details
//       .populate('referredUserId', 'name email') // Include referred user details
//       .exec();

//     // Format response
//     const formattedReferrals = allReferrals.map((referral) => ({
//       id: referral._id,
//       referrer: referral.referrerId
//         ? {
//             id: referral.referrerId._id,
//             name: referral.referrerId.name,
//             email: referral.referrerId.email,
//           }
//         : null,
//       referredUser: referral.referredUserId
//         ? {
//             id: referral.referredUserId._id,
//             name: referral.referredUserId.name,
//             email: referral.referredUserId.email,
//           }
//         : null,
//       referralId: referral.referralId,
//       createdAt: referral.createdAt.toISOString(),
//     }));

//     return NextResponse.json(
//       { referrals: formattedReferrals },
//       { status: 200, headers: corsHeaders }
//     );
//   } catch (error) {
//     console.error('Error fetching all referrals:', error);

//     // Handle specific errors
//     if (error instanceof jwt.JsonWebTokenError) {
//       return NextResponse.json(
//         { error: 'Invalid token' },
//         { status: 401, headers: corsHeaders }
//       );
//     }

//     // Handle other errors
//     return NextResponse.json(
//       { error: 'Internal server error' },
//       { status: 500, headers: corsHeaders }
//     );
//   }
// }



// import { NextRequest, NextResponse } from 'next/server';
// import jwt from 'jsonwebtoken';
// import { connectToDatabase } from '@/lib/dbconnect';
// import { Referral } from '@/models/Referral';
// import { Investment } from '@/models/Investment';

// const corsHeaders = {
//   'Access-Control-Allow-Origin': '*',
//   'Access-Control-Allow-Methods': 'GET, OPTIONS',
//   'Access-Control-Allow-Headers': 'Content-Type, Authorization',
// };

// export async function GET(req: NextRequest) {
//   // Handle preflight OPTIONS request
//   if (req.method === 'OPTIONS') {
//     return NextResponse.json(null, { headers: corsHeaders });
//   }

//   // Verify JWT token and ensure admin role
//   const authHeader = req.headers.get('authorization');
//   const token = authHeader?.split(' ')[1];
//   if (!token) {
//     return NextResponse.json(
//       { error: 'Unauthorized' },
//       { status: 401, headers: corsHeaders }
//     );
//   }

//   try {
//     const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { role: string };
//     if (decoded.role !== 'admin') {
//       return NextResponse.json(
//         { error: 'Forbidden: Admin role required' },
//         { status: 403, headers: corsHeaders }
//       );
//     }

//     await connectToDatabase();

//     // Fetch all referrals
//     const referrals = await Referral.find({})
//       .populate('referrerId', 'name email')
//       .populate('referredUserId', 'name email')
//       .exec();

//     // Determine "active" status for each referral
//     const referralsWithStatus = await Promise.all(
//       referrals.map(async (referral) => {
//         const activeInvestments = await Investment.find({
//           userId: referral.referredUserId,
//           paymentStatus: 'approved',
//         }).exec();

//         return {
//           _id: referral._id,
//           referrer: referral.referrerId,
//           referredUser: referral.referredUserId,
//           referralId: referral.referralId,
//           createdAt: referral.createdAt,
//           active: activeInvestments.length > 0,
//         };
//       })
//     );

//     return NextResponse.json(
//       { referrals: referralsWithStatus },
//       { status: 200, headers: corsHeaders }
//     );
//   } catch (error) {
//     console.error('Error fetching referrals:', error);
//     return NextResponse.json(
//       { error: 'Internal server error' },
//       { status: 500, headers: corsHeaders }
//     );
//   }
// }

// app/api/referrals/route.ts
import { NextRequest, NextResponse } from 'next/server';
import jwt from 'jsonwebtoken';
import { connectToDatabase } from '@/lib/dbconnect';
import { Referral } from '@/models/Referral';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Methods': 'GET, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization',
};

export async function GET(req: NextRequest) {
  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  const { searchParams } = req.nextUrl;
  const page = parseInt(searchParams.get('page') || '1', 10);
  const search = searchParams.get('search') || '';
  const sort = searchParams.get('sort') || 'createdAt';
  const order = searchParams.get('order') === 'desc' ? -1 : 1;
  const status = searchParams.get('status') || 'all';
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
  const exportAll = searchParams.get('exportAll') === 'true'; // Explicit export flag

  const authHeader = req.headers.get('authorization');
  console.log('Authorization Header:', authHeader); // Log the header for debugging

  if (!authHeader) {
    console.error('Missing Authorization header');
    return NextResponse.json(
      { error: 'Unauthorized: Missing Authorization header' },
      { status: 401, headers: corsHeaders }
    );
  }

  const token = authHeader.split(' ')[1];
  console.log('Extracted Token:', token); // Log the token

  if (!token) {
    console.error('Missing token in Authorization header');
    return NextResponse.json(
      { error: 'Unauthorized: Missing token' },
      { status: 401, headers: corsHeaders }
    );
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as { role: string };
    console.log('Decoded Token:', decoded); // Log the decoded token

    if (decoded.role !== 'admin') {
      console.error('Forbidden: User does not have admin role');
      return NextResponse.json(
        { error: 'Forbidden: Admin role required' },
        { status: 403, headers: corsHeaders }
      );
    }

    await connectToDatabase();

    // Define the filter based on search query and filters
    let filter: any = {};

    if (search) {
      filter.$or = [
        { 'referrerId.name': { $regex: search, $options: 'i' } },
        { 'referrerId.email': { $regex: search, $options: 'i' } },
        { 'referredUserId.name': { $regex: search, $options: 'i' } },
        { 'referredUserId.email': { $regex: search, $options: 'i' } },
        { referralId: { $regex: search, $options: 'i' } }
      ];
    }

    const total = await Referral.countDocuments(filter);
    const totalPages = Math.ceil(total / pageSize);

    // For export (when pageSize is large like 1000 OR exportAll flag is set), get ALL records without any limits
    let referrals;
    if (pageSize >= 1000 || exportAll) {
      console.log('EXPORT MODE: Fetching ALL referrals from database (no limits)');
      console.log('Total records in database matching filter:', total);

      referrals = await Referral.find(filter)
        .populate('referrerId', 'name email') // Populate referrer details
        .populate('referredUserId', 'name email') // Populate referred user details
        .sort({ [sort]: order })
        .exec(); // No skip, no limit - get EVERYTHING

      console.log(`✅ EXPORT SUCCESS: Fetched ${referrals.length} of ${total} total referrals`);

      if (referrals.length !== total) {
        console.warn(`⚠️ WARNING: Expected ${total} records but got ${referrals.length}`);
      }
    } else {
      console.log(`Regular pagination mode: Fetching page ${page} with ${pageSize} records`);
      referrals = await Referral.find(filter)
        .populate('referrerId', 'name email') // Populate referrer details
        .populate('referredUserId', 'name email') // Populate referred user details
        .sort({ [sort]: order })
        .skip((page - 1) * pageSize)
        .limit(pageSize)
        .exec();
      console.log(`Fetched ${referrals.length} referrals for page ${page}`);
    }

    // Determine "active" status for each referral and calculate earnings
    const referralsWithStatus = await Promise.all(
      referrals.map(async (referral) => {
        const activeInvestments = await Investment.find({
          userId: referral.referredUserId,
          paymentStatus: 'approved',
        }).exec();

        // Calculate earnings from investments
        const earnings = activeInvestments.map(investment => ({
          investmentAmount: investment.amount,
          percentage: 10, // Assuming 10% referral commission
          earningAmount: investment.amount * 0.1
        }));

        return {
          _id: referral._id,
          referrer: referral.referrerId,
          referredUser: referral.referredUserId,
          referralId: referral.referralId,
          createdAt: referral.createdAt,
          active: activeInvestments.length > 0,
          earnings: earnings
        };
      })
    );

    // Apply status filter after processing
    let filteredReferrals = referralsWithStatus;
    if (status !== 'all') {
      filteredReferrals = referralsWithStatus.filter(ref =>
        status === 'active' ? ref.active : !ref.active
      );
    }

    return NextResponse.json(
      {
        referrals: filteredReferrals,
        totalPages,
        currentPage: page,
        totalReferrals: total,
        pageSize,
        actualCount: filteredReferrals.length, // Actual number of records returned
        isExportMode: pageSize >= 1000 || exportAll,
        exportAll: exportAll
      },
      { status: 200, headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error during JWT verification or fetching referrals:', error);

    const errorMessage =
      error instanceof jwt.JsonWebTokenError
        ? 'Unauthorized: Invalid token'
        : 'Internal server error';

    return NextResponse.json(
      { error: errorMessage },
      { status: error instanceof jwt.JsonWebTokenError ? 401 : 500, headers: corsHeaders }
    );
  }
}
