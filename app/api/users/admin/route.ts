// app/api/users/admin/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import '@/models'; // Import all models

export async function GET(req: NextRequest) {
  const { searchParams } = req.nextUrl;
  const page = parseInt(searchParams.get('page') || '1', 10);
  const search = searchParams.get('search') || '';
  const role = searchParams.get('role') || 'all';
  const status = searchParams.get('status') || 'all';
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);
  const sortBy = searchParams.get('sortBy') || 'name';
  const sortOrder = searchParams.get('sortOrder') === 'asc' ? 1 : -1;

  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    await connectToDatabase();

    // Build filter query - start with basic search like the original
    const filter: any = {};

    // Search filter (like the original working version)
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Role filter (only if not 'all')
    if (role !== 'all') {
      filter.role = role;
    }

    // Status filter (only if not 'all')
    if (status !== 'all') {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      if (status === 'active') {
        filter.updatedAt = { $gte: thirtyDaysAgo };
      } else if (status === 'inactive') {
        filter.updatedAt = { $lt: thirtyDaysAgo };
      }
    }

    const total = await User.countDocuments(filter);
    const totalPages = Math.ceil(total / pageSize);

    // Use the original working query structure without populate to avoid schema errors
    const users = await User.find(filter)
      .sort({ [sortBy]: sortOrder })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .select('name email phone role createdAt updatedAt referralCode')
      .exec();

    // Add basic enhancements without complex queries that might fail
    const enhancedUsers = users.map((user) => {
      const userObj = user.toObject();

      // Calculate if user is active (basic calculation)
      const isActive = new Date(userObj.updatedAt).getTime() > Date.now() - (30 * 24 * 60 * 60 * 1000);

      return {
        ...userObj,
        totalInvestments: 0, // Default values to prevent errors
        totalInvestmentAmount: 0,
        isActive,
        membership: null // Add null membership to prevent errors
      };
    });

    return NextResponse.json(
      {
        users: enhancedUsers,
        totalPages,
        currentPage: page,
        totalUsers: total,
        pageSize
      },
      { headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500, headers: corsHeaders }
    );
  }
}
