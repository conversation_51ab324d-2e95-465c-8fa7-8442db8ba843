// app/api/subscriptions/export/all/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Subscription } from '@/models/Subscription';
import '@/models'; // Import all models

export async function GET(req: NextRequest) {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    await connectToDatabase();

    console.log('Fetching ALL subscriptions for export...');

    // Fetch ALL subscriptions without pagination for export
    const subscriptions = await Subscription.find({})
      .populate('userId', 'name email') // Populate user details
      .populate('plan', 'title percentage') // Populate plan details
      .sort({ createdAt: -1 }) // Sort by newest first
      .exec();

    console.log(`Found ${subscriptions.length} total subscriptions for export`);

    // Transform the data to include calculated fields
    const transformedSubscriptions = subscriptions.map(sub => {
      const startDate = new Date(sub.startDate);
      const endDate = new Date(sub.endDate);
      const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const diffMonths = Math.floor(diffDays / 30);
      
      let duration = '';
      if (diffMonths < 1) {
        duration = `${diffDays} days`;
      } else if (diffMonths < 12) {
        duration = `${diffMonths} months`;
      } else {
        const years = Math.floor(diffMonths / 12);
        const remainingMonths = diffMonths % 12;
        duration = remainingMonths > 0 ? `${years}y ${remainingMonths}m` : `${years} years`;
      }

      return {
        _id: sub._id,
        userId: {
          name: sub.userId?.name || 'N/A',
          email: sub.userId?.email || 'N/A'
        },
        plan: {
          title: sub.plan?.title || 'N/A',
          percentage: sub.plan?.percentage || 0
        },
        startDate: sub.startDate,
        endDate: sub.endDate,
        amount: sub.amount,
        status: sub.status,
        duration: duration,
        roi: sub.plan?.percentage || 0,
        createdAt: sub.createdAt,
        updatedAt: sub.updatedAt
      };
    });

    return NextResponse.json(
      {
        success: true,
        subscriptions: transformedSubscriptions,
        total: transformedSubscriptions.length,
        message: `Successfully fetched ${transformedSubscriptions.length} investments for export`
      },
      { headers: corsHeaders }
    );

  } catch (error) {
    console.error('Error fetching all subscriptions for export:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch subscriptions for export',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500, headers: corsHeaders }
    );
  }
}
