// app/api/subscriptions/admin/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { Subscription } from '@/models/Subscription';
import '@/models'; // Import all models

export async function GET(req: NextRequest) {
  const { searchParams } = req.nextUrl;
  const page = parseInt(searchParams.get('page') || '1', 10);
  const search = searchParams.get('search') || '';
  const sort = searchParams.get('sort') || 'startDate';
  const order = searchParams.get('order') === 'desc' ? -1 : 1;
  const pageSize = 10;

  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    await connectToDatabase();

    // Define the filter based on search query
    const filter = search
      ? { 'userId.name': { $regex: search, $options: 'i' } }
      : {};

    const total = await Subscription.countDocuments(filter);
    const totalPages = Math.ceil(total / pageSize);

    const subscriptions = await Subscription.find(filter)
      .populate('userId', 'name email') // Populate user details
      .populate('plan', 'title percentage') // Populate plan details
      .sort({ [sort]: order })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .exec();

    return NextResponse.json(
      {
        subscriptions,
        totalPages,
      },
      { headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error fetching subscriptions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscriptions' },
      { status: 500, headers: corsHeaders }
    );
  }
}
