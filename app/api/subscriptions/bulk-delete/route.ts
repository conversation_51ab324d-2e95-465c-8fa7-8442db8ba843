import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import { ObjectId } from 'mongodb';

export async function DELETE(request: NextRequest) {
  try {
    const { ids } = await request.json();

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: 'Invalid or empty IDs array' },
        { status: 400 }
      );
    }

    // Validate that all IDs are valid ObjectIds
    const validIds = ids.filter(id => {
      try {
        new ObjectId(id);
        return true;
      } catch {
        return false;
      }
    });

    if (validIds.length === 0) {
      return NextResponse.json(
        { error: 'No valid IDs provided' },
        { status: 400 }
      );
    }

    const { db } = await connectToDatabase();
    
    // Convert string IDs to ObjectIds
    const objectIds = validIds.map(id => new ObjectId(id));
    
    // Delete the subscriptions
    const result = await db.collection('subscriptions').deleteMany({
      _id: { $in: objectIds }
    });

    if (result.deletedCount === 0) {
      return NextResponse.json(
        { error: 'No investments found to delete' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      deletedCount: result.deletedCount,
      message: `Successfully deleted ${result.deletedCount} investment(s)`
    });

  } catch (error) {
    console.error('Error in bulk delete:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
