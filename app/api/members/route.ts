// app/api/members/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/dbconnect';
import { User } from '@/models/User';
import { Subscription } from '@/models/Subscription';
import { Investment } from '@/models/Investment';
import '@/models'; // Import all models

export async function GET(req: NextRequest) {
  const { searchParams } = req.nextUrl;
  const page = parseInt(searchParams.get('page') || '1', 10);
  const search = searchParams.get('search') || '';
  const role = searchParams.get('role') || 'member';
  const status = searchParams.get('status') || 'all';
  const sortBy = searchParams.get('sortBy') || 'createdAt';
  const sortOrder = searchParams.get('sortOrder') === 'asc' ? 1 : -1;
  const pageSize = parseInt(searchParams.get('pageSize') || '10', 10);

  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };

  if (req.method === 'OPTIONS') {
    return NextResponse.json(null, { headers: corsHeaders });
  }

  try {
    await connectToDatabase();

    // Build filter query
    const filter: any = {};
    
    // Filter by role (default to 'member' but allow filtering by other roles)
    if (role !== 'all') {
      filter.role = role;
    }

    // Search filter
    if (search) {
      filter.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
        { phone: { $regex: search, $options: 'i' } }
      ];
    }

    // Status filter (active/inactive based on recent activity)
    if (status !== 'all') {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      if (status === 'active') {
        filter.updatedAt = { $gte: thirtyDaysAgo };
      } else if (status === 'inactive') {
        filter.updatedAt = { $lt: thirtyDaysAgo };
      }
    }

    // Get total count for pagination
    const total = await User.countDocuments(filter);
    const totalPages = Math.ceil(total / pageSize);

    // Fetch members with pagination and sorting
    const members = await User.find(filter)
      .select('name email phone role membership createdAt updatedAt bankDetails referralCode')
      .populate('membership', 'plan startDate endDate status')
      .sort({ [sortBy]: sortOrder })
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .lean();

    // Enhance member data with additional statistics
    const enhancedMembers = await Promise.all(
      members.map(async (member) => {
        // Get investment count and total
        const investments = await Investment.find({ userId: member._id });
        const totalInvestments = investments.length;
        const totalInvestmentAmount = investments.reduce((sum, inv) => sum + inv.amount, 0);
        
        // Get subscription count
        const subscriptions = await Subscription.countDocuments({ userId: member._id });
        
        // Calculate member status
        const isActive = new Date(member.updatedAt).getTime() > Date.now() - (30 * 24 * 60 * 60 * 1000);
        
        return {
          ...member,
          totalInvestments,
          totalInvestmentAmount,
          subscriptions,
          isActive,
          lastActivity: member.updatedAt,
          joinDate: member.createdAt
        };
      })
    );

    return NextResponse.json(
      {
        members: enhancedMembers,
        pagination: {
          currentPage: page,
          totalPages,
          totalMembers: total,
          pageSize,
          hasNext: page < totalPages,
          hasPrev: page > 1
        },
        filters: {
          role,
          status,
          search,
          sortBy,
          sortOrder: sortOrder === 1 ? 'asc' : 'desc'
        }
      },
      { headers: corsHeaders }
    );
  } catch (error) {
    console.error('Error fetching members:', error);
    return NextResponse.json(
      { error: 'Failed to fetch members' },
      { status: 500, headers: corsHeaders }
    );
  }
}

export async function OPTIONS() {
  const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization',
  };
  
  return NextResponse.json(null, { headers: corsHeaders });
}
