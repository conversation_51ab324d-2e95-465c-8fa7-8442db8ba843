
// app/(site)/page.tsx
'use client';

import CTASection from "@/components/home/<USER>";
import HeroSection from "@/components/home/<USER>";
import HowItWorks from "@/components/home/<USER>";
import KeyBenefits from "@/components/home/<USER>";
import PurchasePlans from "@/components/home/<USER>";
// import Testimonials from "@/components/home/<USER>";
import Welcome from "@/components/home/<USER>";
import Loading from "@/components/ui/loading";
import HomeSkeleton from "@/components/ui/HomeSkeleton";
import { usePageLoading } from "@/hooks/usePageLoading";
import React, { useEffect, useState } from 'react';
// import Image from "next/image";

export default function Home() {
  const { isLoading, startLoading, stopLoading } = usePageLoading();
  const [componentsLoaded, setComponentsLoaded] = useState(false);

  useEffect(() => {
    // Simulate loading time for home page components
    const loadComponents = async () => {
      // Simulate API calls or heavy component loading
      await new Promise(resolve => setTimeout(resolve, 2000));
      setComponentsLoaded(true);
    };

    loadComponents();
  }, []);

  if (!componentsLoaded) {
    return <HomeSkeleton />;
  }

  return (
    <>
     <HeroSection/>
     <Welcome/>
     <KeyBenefits/>
     <HowItWorks/>
     {/* <Testimonials/> */}
     <PurchasePlans/>
     <CTASection/>
    </>
  );
}
