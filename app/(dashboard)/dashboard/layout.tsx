// // app/(dashboard)/dashboard/layout.tsx
// 'use client';

// import React from 'react';
// import Sidebar from '@/components/navigation/Sidebar';
// import TopNavbar from '@/components/navigation/TopNavbar';
// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/navigation';

// export default function DashboardLayout({
//   children,
// }: {
//   children: React.ReactNode;
// }) {
//   const { user } = useAuth();
//   const router = useRouter();

//   // Redirect to login if not authenticated
//   if (!user) {
//     router.push('/');
//     return null; // Or show a loading spinner
//   }

//   return (
//     <div className="flex h-screen">
//       <Sidebar />
//       <div className="flex-1 flex flex-col">
//         <TopNavbar />
//         <main className="flex-1 overflow-auto bg-gray-100 p-6">{children}</main>
//       </div>
//     </div>
//   );
// }



// app/(dashboard)/dashboard/layout.tsx
'use client';

import React from 'react';
import Sidebar from '@/components/navigation/Sidebar';
import TopNavbar from '@/components/navigation/TopNavbar';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useAuth();
  const router = useRouter();

  // Redirect to home if not authenticated
  if (!user) {
    router.push('/');
    return null; // Or show a loading spinner
  }

  return (
    <div className="flex h-screen">
      <Sidebar />
      <div className="flex-1 flex flex-col">
        <TopNavbar />
        <main className="flex-1 overflow-auto bg-gray-100 p-6">{children}</main>
      </div>
    </div>
  );
}
