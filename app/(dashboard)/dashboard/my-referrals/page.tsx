'use client';

import ReferralDashboard from '@/components/admin/referrals/ReferralDashboard';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { ReferralsSkeleton } from '@/components/ui/DashboardSkeleton';
import React, { useState, useEffect };

export default function MyReferralsPage() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadReferralData = async () => {
      // Simulate loading referral data
      await new Promise(resolve => setTimeout(resolve, 1800));
      setIsLoading(false);
    };

    loadReferralData();
  }, []);

  if (isLoading) {
    return <ReferralsSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">My Referrals</h1>
        <p>Manage your referral network and track earnings.</p>
      </div>

      <ReferralDashboard />
    </div>
  );
}
