'use client';

import ReferralPyramidTable from '@/components/dashboard/ReferralPyramidTable';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import React, { useState, useEffect };

export default function ReferralBonusesPage() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadReferralBonusData = async () => {
      // Simulate loading referral bonus data
      await new Promise(resolve => setTimeout(resolve, 1600));
      setIsLoading(false);
    };

    loadReferralBonusData();
  }, []);

  return (
    <div className="space-y-6">
      <LoadingWrapper
        isLoading={isLoading}
        loadingText="Loading referral bonus information..."
        loadingVariant="skeleton"
        className="bg-white p-6 rounded-lg shadow-md"
      >
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h1 className="text-2xl font-bold mb-4">My Referral Pyramid</h1>
          <p>View your referral structure and bonus earnings.</p>
        </div>
      </LoadingWrapper>

      <LoadingWrapper
        isLoading={isLoading}
        loadingText="Loading referral pyramid..."
        loadingVariant="skeleton"
      >
        <ReferralPyramidTable />
      </LoadingWrapper>
    </div>
  );
}
