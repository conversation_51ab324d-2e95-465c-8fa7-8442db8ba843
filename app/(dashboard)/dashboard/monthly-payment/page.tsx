'use client';

import MonthlyPaymentsTable from '@/components/dashboard/MonthlyPaymentTable';
import LoadingWrapper from '@/components/ui/LoadingWrapper';
import { MonthlyPaymentSkeleton } from '@/components/ui/DashboardSkeleton';
import React, { useState, useEffect };

export default function MonthlyPaymentPage() {
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadPaymentData = async () => {
      // Simulate loading payment data
      await new Promise(resolve => setTimeout(resolve, 1200));
      setIsLoading(false);
    };

    loadPaymentData();
  }, []);

  if (isLoading) {
    return <MonthlyPaymentSkeleton />;
  }

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h1 className="text-2xl font-bold mb-4">Monthly Payments</h1>
        <p>Track your monthly payment schedule and history.</p>
      </div>

      <MonthlyPaymentsTable />
    </div>
  );
}
