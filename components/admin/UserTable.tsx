// // components/admin/UserTable.tsx

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table';
// import { Badge } from '@/components/ui/badge';
// import { Button } from '@/components/ui/button';
// import { Search } from 'lucide-react';
// import { Skeleton } from '@/components/ui/skeleton';
// import { UserDetailsOverlay } from './UserDetailsOverlay';

// interface User {
//   _id: string;
//   name: string;
//   email: string;
//   phone?: string;
//   membership?: { name: string } | null;
//   role: 'admin' | 'author' | 'editor' | 'member' | 'investor';
// }

// const UserTable: React.FC = () => {
//   const [users, setUsers] = useState<User[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [sortColumn, setSortColumn] = useState<string | null>(null);
//   const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
//   const [selectedUser, setSelectedUser] = useState<User | null>(null);
//   const [isOverlayOpen, setIsOverlayOpen] = useState(false);

//   useEffect(() => {
//     fetchUsers();
//   }, [searchQuery, sortColumn, sortOrder]);

//   const fetchUsers = async () => {
//     setLoading(true);
//     try {
//       const response = await fetch(
//         `/api/users/admin?search=${searchQuery}&sort=${sortColumn}&order=${sortOrder}`
//       );
//       const data = await response.json();
//       setUsers(data.users);
//     } catch (error) {
//       console.error('Error fetching users:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleSort = (column: string) => {
//     setSortOrder(sortColumn === column && sortOrder === 'asc' ? 'desc' : 'asc');
//     setSortColumn(column);
//   };

//   const renderSkeletonRow = () => (
//     <TableRow>
//       <TableCell>
//         <Skeleton className="h-4 w-[250px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[150px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[100px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[100px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[80px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[80px]" />
//       </TableCell>
//     </TableRow>
//   );

//   const handleViewClick = (user: User) => {
//     setSelectedUser(user);
//     setIsOverlayOpen(true);
//   };

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>User Data</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="flex justify-between items-center mb-4">
//           <div className="relative w-64">
//             <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
//             <Input
//               type="text"
//               placeholder="Search by name or email"
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//               className="pl-8"
//             />
//           </div>
//         </div>

//         <div className="rounded-md border">
//           <Table>
//             <TableHeader>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => handleSort('name')}>
//                   Name
//                 </Button>
//               </TableHead>
//               <TableHead>Email</TableHead>
//               <TableHead>Phone</TableHead>
//               <TableHead>Membership</TableHead>
//               <TableHead>Role</TableHead>
//               <TableHead>Actions</TableHead>
//             </TableHeader>
//             <TableBody>
//               {loading
//                 ? Array.from({ length: 5 }).map((_, index) => (
//                     <TableRow key={index}>{renderSkeletonRow()}</TableRow>
//                   ))
//                 : users.length > 0
//                 ? users.map((user) => (
//                     <TableRow key={user._id}>
//                       <TableCell>{user.name}</TableCell>
//                       <TableCell>{user.email}</TableCell>
//                       <TableCell>{user.phone || 'N/A'}</TableCell>
//                       <TableCell>{user.membership?.name || 'N/A'}</TableCell>
//                       <TableCell>
//                         <Badge>{user.role}</Badge>
//                       </TableCell>
//                       <TableCell>
//                         <Button variant="secondary" size="sm" onClick={() => handleViewClick(user)}>
//                           View
//                         </Button>
//                       </TableCell>
//                     </TableRow>
//                   ))
//                 : (
//                   <TableRow>
//                     <TableCell colSpan={6} className="text-center text-muted-foreground">
//                       No users found
//                     </TableCell>
//                   </TableRow>
//                 )}
//             </TableBody>
//           </Table>
//         </div>

//         <UserDetailsOverlay
//           isOpen={isOverlayOpen}
//           onClose={() => setIsOverlayOpen(false)}
//           user={selectedUser}
//         />
//       </CardContent>
//     </Card>
//   );
// };

// export default UserTable;



// 'use client';

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table';
// import { Skeleton } from '@/components/ui/skeleton';
// import { Button } from '@/components/ui/button';
// import { Search } from 'lucide-react';

// interface User {
//   _id: string;
//   name: string;
//   email: string;
//   phone?: string;
//   role: string;
//   membership?: { name: string } | null;
// }

// const UserTable: React.FC = () => {
//   const [users, setUsers] = useState<User[]>([]); // Initialize as an empty array
//   const [loading, setLoading] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [currentPage, setCurrentPage] = useState(1);
//   const [totalPages, setTotalPages] = useState(1);

//   useEffect(() => {
//     fetchUsers();
//   }, [currentPage, searchQuery]);

//   const fetchUsers = async () => {
//     setLoading(true);
//     try {
//       const response = await fetch(
//         `/api/users/admin?page=${currentPage}&search=${searchQuery}`
//       );
//       const data = await response.json();
//       setUsers(data.users || []); // Ensure users is always an array
//       setTotalPages(data.totalPages || 1);
//     } catch (error) {
//       console.error('Error fetching users:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const renderSkeletonRow = () => (
//     <TableRow>
//       <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
//       <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
//       <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
//       <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
//     </TableRow>
//   );

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>User Data</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="flex justify-between items-center mb-4">
//           <div className="relative w-64">
//             <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
//             <Input
//               type="text"
//               placeholder="Search by user"
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//               className="pl-8"
//             />
//           </div>
//         </div>

//         <div className="rounded-md border">
//           <Table>
//             <TableHeader>
//               <TableHead>Name</TableHead>
//               <TableHead>Email</TableHead>
//               <TableHead>Phone</TableHead>
//               <TableHead>Role</TableHead>
//             </TableHeader>
//             <TableBody>
//               {loading
//                 ? Array.from({ length: 5 }).map((_, index) => (
//                     <TableRow key={index}>{renderSkeletonRow()}</TableRow>
//                   ))
//                 : users?.length > 0 // Optional chaining for safety
//                 ? users.map((user) => (
//                     <TableRow key={user._id}>
//                       <TableCell>{user.name}</TableCell>
//                       <TableCell>{user.email}</TableCell>
//                       <TableCell>{user.phone || 'N/A'}</TableCell>
//                       <TableCell>{user.role}</TableCell>
//                     </TableRow>
//                   ))
//                 : (
//                   <TableRow>
//                     <TableCell colSpan={4} className="text-center text-muted-foreground">
//                       No users found
//                     </TableCell>
//                   </TableRow>
//                 )}
//             </TableBody>
//           </Table>
//         </div>

//         <div className="flex items-center justify-end space-x-2 py-4">
//           <Button
//             variant="outline"
//             size="sm"
//             onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
//             disabled={currentPage === 1}
//           >
//             Previous
//           </Button>
//           <Button
//             variant="outline"
//             size="sm"
//             onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
//             disabled={currentPage === totalPages}
//           >
//             Next
//           </Button>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default UserTable;




// components/admin/UserTable.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Download,
  FileText,
  FileSpreadsheet,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Users
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import UserDetailsOverlay from './UserDetailsOverlay';
import { downloadUsersPDF } from '@/lib/pdfExport';
import { downloadUsersExcel } from '@/lib/excelExport';
import toast from 'react-hot-toast';

interface User {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  membership?: { name: string } | null;
  createdAt: string;
  updatedAt: string;
  referralCode?: string;
  bankDetails?: {
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
  };
  totalInvestments?: number;
  totalInvestmentAmount?: number;
  isActive?: boolean;
}

const UserTable: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, [searchQuery, currentPage, roleFilter, statusFilter, pageSize, sortBy, sortOrder]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        search: searchQuery,
        page: currentPage.toString(),
        role: roleFilter,
        status: statusFilter,
        pageSize: pageSize.toString(),
        sortBy,
        sortOrder
      });

      const response = await fetch(`/api/users/admin?${params}`);
      const data = await response.json();
      setUsers(data.users || []);
      setTotalPages(data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching users:', error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const openUserDetails = (user: User) => {
    setSelectedUser(user);
    setIsOverlayOpen(true);
  };

  const closeUserDetails = () => {
    setSelectedUser(null);
    setIsOverlayOpen(false);
  };

  const exportToPDF = async () => {
    setExporting(true);
    try {
      // Fetch all users for export (not paginated)
      const params = new URLSearchParams({
        search: searchQuery,
        role: roleFilter,
        status: statusFilter,
        sortBy,
        sortOrder,
        pageSize: '1000' // Get all users for export
      });

      const response = await fetch(`/api/users/admin?${params}`);
      if (response.ok) {
        const data = await response.json();
        const exportUsers = data.users.map((user: User) => ({
          name: user.name,
          email: user.email,
          phone: user.phone || 'N/A',
          role: user.role,
          status: user.isActive ? 'Active' : 'Inactive',
          totalInvestments: user.totalInvestments || 0,
          totalInvestmentAmount: user.totalInvestmentAmount || 0,
          referralCode: user.referralCode || 'N/A',
          bankName: user.bankDetails?.bankName || 'N/A',
          accountNumber: user.bankDetails?.accountNumber || 'N/A',
          accountName: user.bankDetails?.accountName || 'N/A',
          joinDate: new Date(user.createdAt).toLocaleDateString(),
          lastActivity: new Date(user.updatedAt).toLocaleDateString()
        }));

        console.log('Attempting PDF export with', exportUsers.length, 'users');
        console.log('Sample user data:', exportUsers[0]);

        // Use the comprehensive PDF export with error handling
        try {
          downloadUsersPDF(exportUsers, {
            search: searchQuery,
            role: roleFilter,
            status: statusFilter
          });
          console.log('PDF export function completed');
        } catch (pdfError) {
          console.error('PDF generation error:', pdfError);
          throw new Error(`PDF generation failed: ${pdfError.message}`);
        }

        // Show success message
        toast.success('PDF export completed successfully! 📄');
      } else {
        console.error('Export failed:', response.status, response.statusText);
        toast.error('PDF export failed. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('PDF export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const exportToExcel = async () => {
    setExporting(true);
    try {
      // Fetch all users for export (not paginated)
      const params = new URLSearchParams({
        search: searchQuery,
        role: roleFilter,
        status: statusFilter,
        sortBy,
        sortOrder,
        pageSize: '1000' // Get all users for export
      });

      const response = await fetch(`/api/users/admin?${params}`);
      if (response.ok) {
        const data = await response.json();
        const exportUsers = data.users.map((user: User) => ({
          name: user.name,
          email: user.email,
          phone: user.phone || 'N/A',
          role: user.role,
          status: user.isActive ? 'Active' : 'Inactive',
          totalInvestments: user.totalInvestments || 0,
          totalInvestmentAmount: user.totalInvestmentAmount || 0,
          referralCode: user.referralCode || 'N/A',
          bankName: user.bankDetails?.bankName || 'N/A',
          accountNumber: user.bankDetails?.accountNumber || 'N/A',
          accountName: user.bankDetails?.accountName || 'N/A',
          joinDate: new Date(user.createdAt).toLocaleDateString(),
          lastActivity: new Date(user.updatedAt).toLocaleDateString()
        }));

        downloadUsersExcel(exportUsers, {
          search: searchQuery,
          role: roleFilter,
          status: statusFilter
        });

        // Show success message
        toast.success('Excel export completed successfully! 📊');
      } else {
        console.error('Export failed:', response.status, response.statusText);
        toast.error('Excel export failed. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting Excel:', error);
      toast.error('Excel export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const getStatusBadge = (user: User) => {
    const isActive = user.isActive ?? (new Date(user.updatedAt).getTime() > Date.now() - (30 * 24 * 60 * 60 * 1000));
    return (
      <Badge variant={isActive ? "default" : "secondary"}>
        {isActive ? "Active" : "Inactive"}
      </Badge>
    );
  };

  const getRoleBadge = (role: string) => {
    const colors = {
      admin: "destructive",
      investor: "default",
      member: "secondary",
      author: "outline",
      editor: "outline"
    };
    return (
      <Badge variant={colors[role as keyof typeof colors] || "outline"}>
        {role}
      </Badge>
    );
  };

  const renderSkeletonRow = () => (
    <TableRow>
      <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
    </TableRow>
  );

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Export Options */}
      <div className="flex items-center justify-between p-6 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 rounded-t-xl border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <Users className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Member Database
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {users.length} members • Real-time data
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                disabled={exporting}
                className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700 shadow-sm"
              >
                {exporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
              <DropdownMenuLabel className="text-slate-700 dark:text-slate-300">
                Export Options
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
              <DropdownMenuItem
                onClick={exportToPDF}
                disabled={exporting}
                className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800"
              >
                <FileText className="h-4 w-4 mr-2 text-red-500" />
                <div className="flex-1">
                  <div className="font-medium">Export as PDF</div>
                  <div className="text-xs text-slate-500">Professional report format</div>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={exportToExcel}
                disabled={exporting}
                className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800"
              >
                <FileSpreadsheet className="h-4 w-4 mr-2 text-green-500" />
                <div className="flex-1">
                  <div className="font-medium">Export as Excel</div>
                  <div className="text-xs text-slate-500">Spreadsheet with analytics</div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="px-6">
        {/* Enhanced Filters and Search */}
        <div className="space-y-4 mb-6">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              type="text"
              placeholder="Search members by name, email, phone, or referral code..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-3 bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 focus:border-blue-500 dark:focus:border-blue-400 rounded-xl shadow-sm"
            />
          </div>

          {/* Filter Controls */}
          <div className="flex flex-wrap items-center gap-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Filters:</span>
            </div>

            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[140px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue placeholder="Role" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="investor">Investor</SelectItem>
                <SelectItem value="member">Member</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="author">Author</SelectItem>
                <SelectItem value="editor">Editor</SelectItem>
              </SelectContent>
            </Select>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>

            <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
              <SelectTrigger className="w-[100px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            {(searchQuery || roleFilter !== 'all' || statusFilter !== 'all') && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchQuery('');
                  setRoleFilter('all');
                  setStatusFilter('all');
                }}
                className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
              >
                Clear filters
              </Button>
            )}
          </div>
        </div>

        <div className="rounded-xl border border-slate-200 dark:border-slate-700 overflow-hidden bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm">
          <Table>
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 border-b border-slate-200 dark:border-slate-600">
                <TableHead
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors font-semibold text-slate-700 dark:text-slate-300"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>Name</span>
                    {sortBy === 'name' && (
                      <span className="ml-1 text-blue-600">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors font-semibold text-slate-700 dark:text-slate-300"
                  onClick={() => handleSort('email')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Email</span>
                    {sortBy === 'email' && (
                      <span className="ml-1 text-blue-600">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Phone</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Role</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Status</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Investments</TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-600 transition-colors font-semibold text-slate-700 dark:text-slate-300"
                  onClick={() => handleSort('createdAt')}
                >
                  <div className="flex items-center space-x-1">
                    <span>Joined</span>
                    {sortBy === 'createdAt' && (
                      <span className="ml-1 text-blue-600">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: pageSize }).map((_, index) => (
                    <TableRow key={index}>{renderSkeletonRow()}</TableRow>
                  ))
                : users.length > 0
                ? users.map((user) => (
                    <TableRow key={user._id} className="hover:bg-muted/50">
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.phone || 'N/A'}</TableCell>
                      <TableCell>{getRoleBadge(user.role)}</TableCell>
                      <TableCell>{getStatusBadge(user)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{user.totalInvestments ?? 0} investments</div>
                          <div className="text-muted-foreground">
                            ${(user.totalInvestmentAmount ?? 0).toLocaleString()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(user.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => openUserDetails(user)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Member
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Member
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center text-muted-foreground py-8">
                      No members found matching your criteria
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>

        {/* Enhanced Pagination */}
        <div className="flex items-center justify-between p-6 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 border-t border-slate-200 dark:border-slate-700 rounded-b-xl">
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Showing <span className="font-bold text-blue-600">{users.length}</span> of{' '}
              <span className="font-bold text-blue-600">{totalPages * pageSize}</span> members
            </div>
            {(searchQuery || roleFilter !== 'all' || statusFilter !== 'all') && (
              <div className="text-xs text-slate-500 dark:text-slate-400 bg-blue-50 dark:bg-blue-900/20 px-2 py-1 rounded-md">
                Filtered results
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                if (pageNum > totalPages) return null;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={`w-8 h-8 p-0 ${
                      currentPage === pageNum
                        ? 'bg-blue-600 hover:bg-blue-700 text-white border-blue-600'
                        : 'bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700'
                    }`}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <UserDetailsOverlay
          isOpen={isOverlayOpen}
          onClose={closeUserDetails}
          user={selectedUser}
        />
      </div>
    </div>
  );
};

export default UserTable;
