// // components/admin/UserTable.tsx

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table';
// import { Badge } from '@/components/ui/badge';
// import { Button } from '@/components/ui/button';
// import { Search } from 'lucide-react';
// import { Skeleton } from '@/components/ui/skeleton';
// import { UserDetailsOverlay } from './UserDetailsOverlay';

// interface User {
//   _id: string;
//   name: string;
//   email: string;
//   phone?: string;
//   membership?: { name: string } | null;
//   role: 'admin' | 'author' | 'editor' | 'member' | 'investor';
// }

// const UserTable: React.FC = () => {
//   const [users, setUsers] = useState<User[]>([]);
//   const [loading, setLoading] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [sortColumn, setSortColumn] = useState<string | null>(null);
//   const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
//   const [selectedUser, setSelectedUser] = useState<User | null>(null);
//   const [isOverlayOpen, setIsOverlayOpen] = useState(false);

//   useEffect(() => {
//     fetchUsers();
//   }, [searchQuery, sortColumn, sortOrder]);

//   const fetchUsers = async () => {
//     setLoading(true);
//     try {
//       const response = await fetch(
//         `/api/users/admin?search=${searchQuery}&sort=${sortColumn}&order=${sortOrder}`
//       );
//       const data = await response.json();
//       setUsers(data.users);
//     } catch (error) {
//       console.error('Error fetching users:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleSort = (column: string) => {
//     setSortOrder(sortColumn === column && sortOrder === 'asc' ? 'desc' : 'asc');
//     setSortColumn(column);
//   };

//   const renderSkeletonRow = () => (
//     <TableRow>
//       <TableCell>
//         <Skeleton className="h-4 w-[250px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[150px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[100px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[100px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[80px]" />
//       </TableCell>
//       <TableCell>
//         <Skeleton className="h-4 w-[80px]" />
//       </TableCell>
//     </TableRow>
//   );

//   const handleViewClick = (user: User) => {
//     setSelectedUser(user);
//     setIsOverlayOpen(true);
//   };

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>User Data</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="flex justify-between items-center mb-4">
//           <div className="relative w-64">
//             <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
//             <Input
//               type="text"
//               placeholder="Search by name or email"
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//               className="pl-8"
//             />
//           </div>
//         </div>

//         <div className="rounded-md border">
//           <Table>
//             <TableHeader>
//               <TableHead>
//                 <Button variant="ghost" onClick={() => handleSort('name')}>
//                   Name
//                 </Button>
//               </TableHead>
//               <TableHead>Email</TableHead>
//               <TableHead>Phone</TableHead>
//               <TableHead>Membership</TableHead>
//               <TableHead>Role</TableHead>
//               <TableHead>Actions</TableHead>
//             </TableHeader>
//             <TableBody>
//               {loading
//                 ? Array.from({ length: 5 }).map((_, index) => (
//                     <TableRow key={index}>{renderSkeletonRow()}</TableRow>
//                   ))
//                 : users.length > 0
//                 ? users.map((user) => (
//                     <TableRow key={user._id}>
//                       <TableCell>{user.name}</TableCell>
//                       <TableCell>{user.email}</TableCell>
//                       <TableCell>{user.phone || 'N/A'}</TableCell>
//                       <TableCell>{user.membership?.name || 'N/A'}</TableCell>
//                       <TableCell>
//                         <Badge>{user.role}</Badge>
//                       </TableCell>
//                       <TableCell>
//                         <Button variant="secondary" size="sm" onClick={() => handleViewClick(user)}>
//                           View
//                         </Button>
//                       </TableCell>
//                     </TableRow>
//                   ))
//                 : (
//                   <TableRow>
//                     <TableCell colSpan={6} className="text-center text-muted-foreground">
//                       No users found
//                     </TableCell>
//                   </TableRow>
//                 )}
//             </TableBody>
//           </Table>
//         </div>

//         <UserDetailsOverlay
//           isOpen={isOverlayOpen}
//           onClose={() => setIsOverlayOpen(false)}
//           user={selectedUser}
//         />
//       </CardContent>
//     </Card>
//   );
// };

// export default UserTable;



// 'use client';

// import React, { useState, useEffect } from 'react';
// import {
//   Card,
//   CardContent,
//   CardHeader,
//   CardTitle,
// } from '@/components/ui/card';
// import { Input } from '@/components/ui/input';
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from '@/components/ui/table';
// import { Skeleton } from '@/components/ui/skeleton';
// import { Button } from '@/components/ui/button';
// import { Search } from 'lucide-react';

// interface User {
//   _id: string;
//   name: string;
//   email: string;
//   phone?: string;
//   role: string;
//   membership?: { name: string } | null;
// }

// const UserTable: React.FC = () => {
//   const [users, setUsers] = useState<User[]>([]); // Initialize as an empty array
//   const [loading, setLoading] = useState(true);
//   const [searchQuery, setSearchQuery] = useState('');
//   const [currentPage, setCurrentPage] = useState(1);
//   const [totalPages, setTotalPages] = useState(1);

//   useEffect(() => {
//     fetchUsers();
//   }, [currentPage, searchQuery]);

//   const fetchUsers = async () => {
//     setLoading(true);
//     try {
//       const response = await fetch(
//         `/api/users/admin?page=${currentPage}&search=${searchQuery}`
//       );
//       const data = await response.json();
//       setUsers(data.users || []); // Ensure users is always an array
//       setTotalPages(data.totalPages || 1);
//     } catch (error) {
//       console.error('Error fetching users:', error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const renderSkeletonRow = () => (
//     <TableRow>
//       <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
//       <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
//       <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
//       <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
//     </TableRow>
//   );

//   return (
//     <Card>
//       <CardHeader>
//         <CardTitle>User Data</CardTitle>
//       </CardHeader>
//       <CardContent>
//         <div className="flex justify-between items-center mb-4">
//           <div className="relative w-64">
//             <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
//             <Input
//               type="text"
//               placeholder="Search by user"
//               value={searchQuery}
//               onChange={(e) => setSearchQuery(e.target.value)}
//               className="pl-8"
//             />
//           </div>
//         </div>

//         <div className="rounded-md border">
//           <Table>
//             <TableHeader>
//               <TableHead>Name</TableHead>
//               <TableHead>Email</TableHead>
//               <TableHead>Phone</TableHead>
//               <TableHead>Role</TableHead>
//             </TableHeader>
//             <TableBody>
//               {loading
//                 ? Array.from({ length: 5 }).map((_, index) => (
//                     <TableRow key={index}>{renderSkeletonRow()}</TableRow>
//                   ))
//                 : users?.length > 0 // Optional chaining for safety
//                 ? users.map((user) => (
//                     <TableRow key={user._id}>
//                       <TableCell>{user.name}</TableCell>
//                       <TableCell>{user.email}</TableCell>
//                       <TableCell>{user.phone || 'N/A'}</TableCell>
//                       <TableCell>{user.role}</TableCell>
//                     </TableRow>
//                   ))
//                 : (
//                   <TableRow>
//                     <TableCell colSpan={4} className="text-center text-muted-foreground">
//                       No users found
//                     </TableCell>
//                   </TableRow>
//                 )}
//             </TableBody>
//           </Table>
//         </div>

//         <div className="flex items-center justify-end space-x-2 py-4">
//           <Button
//             variant="outline"
//             size="sm"
//             onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
//             disabled={currentPage === 1}
//           >
//             Previous
//           </Button>
//           <Button
//             variant="outline"
//             size="sm"
//             onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
//             disabled={currentPage === totalPages}
//           >
//             Next
//           </Button>
//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default UserTable;




// components/admin/UserTable.tsx
'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Download,
  FileText,
  FileSpreadsheet,
  Filter,
  ChevronLeft,
  ChevronRight,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import UserDetailsOverlay from './UserDetailsOverlay';

interface User {
  _id: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  membership?: { name: string } | null;
  createdAt: string;
  updatedAt: string;
  referralCode?: string;
  bankDetails?: {
    accountName?: string;
    accountNumber?: string;
    bankName?: string;
  };
  totalInvestments?: number;
  totalInvestmentAmount?: number;
  isActive?: boolean;
}

const UserTable: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [pageSize, setPageSize] = useState(10);
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [exporting, setExporting] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, [searchQuery, currentPage, roleFilter, statusFilter, pageSize, sortBy, sortOrder]);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        search: searchQuery,
        page: currentPage.toString(),
        role: roleFilter,
        status: statusFilter,
        pageSize: pageSize.toString(),
        sortBy,
        sortOrder
      });

      const response = await fetch(`/api/users/admin?${params}`);
      const data = await response.json();
      setUsers(data.users || []);
      setTotalPages(data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching users:', error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  const openUserDetails = (user: User) => {
    setSelectedUser(user);
    setIsOverlayOpen(true);
  };

  const closeUserDetails = () => {
    setSelectedUser(null);
    setIsOverlayOpen(false);
  };

  const exportToPDF = async () => {
    setExporting(true);
    try {
      const params = new URLSearchParams({
        format: 'pdf',
        search: searchQuery,
        role: roleFilter,
        status: statusFilter,
        sortBy,
        sortOrder
      });

      const response = await fetch(`/api/users/export?${params}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `golden-miller-members-report-${new Date().toISOString().split('T')[0]}.html`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error('Export failed:', response.status, response.statusText);
        alert('Export failed. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
      alert('Export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const exportToExcel = async () => {
    setExporting(true);
    try {
      const params = new URLSearchParams({
        format: 'excel',
        search: searchQuery,
        role: roleFilter,
        status: statusFilter,
        sortBy,
        sortOrder
      });

      const response = await fetch(`/api/users/export?${params}`);
      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `golden-miller-members-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        console.error('Export failed:', response.status, response.statusText);
        alert('Export failed. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting Excel:', error);
      alert('Export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const handlePageChange = (newPage: number) => {
    setCurrentPage(newPage);
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const getStatusBadge = (user: User) => {
    const isActive = user.isActive ?? (new Date(user.updatedAt).getTime() > Date.now() - (30 * 24 * 60 * 60 * 1000));
    return (
      <Badge variant={isActive ? "default" : "secondary"}>
        {isActive ? "Active" : "Inactive"}
      </Badge>
    );
  };

  const getRoleBadge = (role: string) => {
    const colors = {
      admin: "destructive",
      investor: "default",
      member: "secondary",
      author: "outline",
      editor: "outline"
    };
    return (
      <Badge variant={colors[role as keyof typeof colors] || "outline"}>
        {role}
      </Badge>
    );
  };

  const renderSkeletonRow = () => (
    <TableRow>
      <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[200px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
    </TableRow>
  );

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <CardTitle>Member Management</CardTitle>
          <div className="flex gap-2">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" disabled={exporting}>
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuLabel>Export Options</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={exportToPDF}>
                  <FileText className="h-4 w-4 mr-2" />
                  Export as PDF
                </DropdownMenuItem>
                <DropdownMenuItem onClick={exportToExcel}>
                  <FileSpreadsheet className="h-4 w-4 mr-2" />
                  Export as Excel
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Filters and Search */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search by name, email, or phone..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8"
            />
          </div>
          <Select value={roleFilter} onValueChange={setRoleFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Role" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Roles</SelectItem>
              <SelectItem value="investor">Investor</SelectItem>
              <SelectItem value="member">Member</SelectItem>
              <SelectItem value="admin">Admin</SelectItem>
              <SelectItem value="author">Author</SelectItem>
              <SelectItem value="editor">Editor</SelectItem>
            </SelectContent>
          </Select>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
          <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
            <SelectTrigger className="w-[100px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('name')}
                >
                  <div className="flex items-center">
                    Name
                    {sortBy === 'name' && (
                      <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('email')}
                >
                  <div className="flex items-center">
                    Email
                    {sortBy === 'email' && (
                      <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Role</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Investments</TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort('createdAt')}
                >
                  <div className="flex items-center">
                    Joined
                    {sortBy === 'createdAt' && (
                      <span className="ml-1">{sortOrder === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: pageSize }).map((_, index) => (
                    <TableRow key={index}>{renderSkeletonRow()}</TableRow>
                  ))
                : users.length > 0
                ? users.map((user) => (
                    <TableRow key={user._id} className="hover:bg-muted/50">
                      <TableCell className="font-medium">{user.name}</TableCell>
                      <TableCell>{user.email}</TableCell>
                      <TableCell>{user.phone || 'N/A'}</TableCell>
                      <TableCell>{getRoleBadge(user.role)}</TableCell>
                      <TableCell>{getStatusBadge(user)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{user.totalInvestments ?? 0} investments</div>
                          <div className="text-muted-foreground">
                            ${(user.totalInvestmentAmount ?? 0).toLocaleString()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {new Date(user.createdAt).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => openUserDetails(user)}>
                              <Eye className="h-4 w-4 mr-2" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Member
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Member
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center text-muted-foreground py-8">
                      No members found matching your criteria
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="text-sm text-muted-foreground">
            Showing {users.length} of {totalPages * pageSize} members
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                if (pageNum > totalPages) return null;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <UserDetailsOverlay
          isOpen={isOverlayOpen}
          onClose={closeUserDetails}
          user={selectedUser}
        />
      </CardContent>
    </Card>
  );
};

export default UserTable;
