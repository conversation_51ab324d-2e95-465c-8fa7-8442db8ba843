'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ArrowUpDown, 
  Search, 
  Download,
  FileText,
  FileSpreadsheet,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Users,
  DollarSign,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { downloadReferralsPDF, downloadReferralsExcel } from '@/lib/referralExport';
import toast from 'react-hot-toast';

interface Referral {
  _id: string;
  referrer: { name: string; email: string };
  referredUser: { _id: string; name: string; email: string } | null;
  referralId: string;
  createdAt: string;
  active: boolean;
  earnings?: {
    investmentAmount: number;
    percentage: number;
    earningAmount: number;
  }[];
}

const ModernReferralTable: React.FC = () => {
  const [referrals, setReferrals] = useState<Referral[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [exporting, setExporting] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    fetchReferrals();
  }, [searchQuery, sortColumn, sortOrder, statusFilter, currentPage, pageSize]);

  const fetchReferrals = async () => {
    setLoading(true);
    try {
      console.log('🔍 Fetching referrals with params:', {
        search: searchQuery,
        sort: sortColumn,
        order: sortOrder,
        status: statusFilter,
        page: currentPage,
        pageSize: pageSize
      });

      // First try the debug endpoint to understand the data structure
      const debugResponse = await fetch('/api/admin/referrals/debug', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (debugResponse.ok) {
        const debugData = await debugResponse.json();
        console.log('🔍 Debug analysis:', debugData);

        // Determine which data source to use
        const useUserModel = debugData.recommendations?.primaryDataSource === 'user_relationships';
        console.log(`📊 Using data source: ${useUserModel ? 'User Model' : 'Referral Model'}`);

        // Fetch actual referrals
        const params = new URLSearchParams({
          search: searchQuery,
          sort: sortColumn || 'createdAt',
          order: sortOrder,
          status: statusFilter,
          page: currentPage.toString(),
          pageSize: pageSize.toString(),
          useUserModel: useUserModel.toString()
        });

        const response = await fetch(`/api/admin/referrals?${params}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Referrals fetched successfully:', {
            count: data.referrals?.length,
            total: data.totalReferrals,
            dataSource: data.dataSource,
            stats: data.stats
          });

          setReferrals(data.referrals || []);
          setTotalPages(data.totalPages || 1);

          if (data.referrals?.length === 0 && data.totalReferrals === 0) {
            console.warn('⚠️ No referrals found in database');
            toast.error('No referrals found in the system');
          }
        } else {
          const errorData = await response.json();
          console.error('❌ API Error:', errorData);
          throw new Error(errorData.error || 'Failed to fetch referrals');
        }
      } else {
        throw new Error('Debug endpoint failed');
      }
    } catch (error) {
      console.error('❌ Error fetching referrals:', error);

      // Show user-friendly error
      toast.error('Failed to load referrals. Please try again.');

      // Fallback to mock data for development
      console.log('🔄 Using mock data as fallback');
      const mockData = generateMockReferrals();
      setReferrals(mockData);
      setTotalPages(Math.ceil(mockData.length / pageSize));
    } finally {
      setLoading(false);
    }
  };

  // Mock data generator for development
  const generateMockReferrals = (): Referral[] => {
    return Array.from({ length: 50 }, (_, i) => ({
      _id: `ref_${i + 1}`,
      referrer: { 
        name: `Referrer ${i + 1}`, 
        email: `referrer${i + 1}@example.com` 
      },
      referredUser: {
        _id: `user_${i + 1}`,
        name: `Referred User ${i + 1}`,
        email: `referred${i + 1}@example.com`
      },
      referralId: `REF${String(i + 1).padStart(6, '0')}`,
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      active: Math.random() > 0.3,
      earnings: [{
        investmentAmount: Math.floor(Math.random() * 50000) + 5000,
        percentage: 10,
        earningAmount: Math.floor(Math.random() * 5000) + 500
      }]
    }));
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(referrals.map(ref => ref._id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;
    
    const confirmMessage = `⚠️ DELETE CONFIRMATION\n\nYou are about to permanently delete ${selectedItems.length} referral(s).\n\nThis action cannot be undone and will remove:\n• Referral records\n• Associated earnings data\n• Commission history\n\nAre you sure you want to proceed?`;
    
    if (confirm(confirmMessage)) {
      setDeleting(true);
      
      try {
        console.log('Deleting referrals:', selectedItems);
        
        const response = await fetch('/api/admin/referrals/bulk-delete', {
          method: 'DELETE',
          headers: { 
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ids: selectedItems })
        });
        
        const data = await response.json();
        
        if (response.ok) {
          // Remove deleted items from local state immediately for better UX
          setReferrals(prev => prev.filter(ref => !selectedItems.includes(ref._id)));
          setSelectedItems([]);
          
          // Show success message
          const successMessage = `✅ SUCCESS\n\nSuccessfully deleted ${data.deletedCount} referral(s).\n\nThe referral records have been permanently removed from the system.`;
          toast.success(`Successfully deleted ${data.deletedCount} referral(s)! 🗑️`);
          
          // Refresh the data to ensure consistency
          await fetchReferrals();
        } else {
          console.error('Delete failed:', data);
          const errorMessage = `❌ DELETE FAILED\n\n${data.error || 'Unknown error occurred'}\n\nPlease try again or contact support if the problem persists.`;
          toast.error('Failed to delete referrals. Please try again.');
        }
      } catch (error) {
        console.error('Error deleting referrals:', error);
        const networkErrorMessage = `🌐 NETWORK ERROR\n\nFailed to connect to the server.\n\nPlease check your internet connection and try again.`;
        toast.error('Network error occurred while deleting referrals.');
      } finally {
        setDeleting(false);
      }
    }
  };

  const exportToPDF = async () => {
    setExporting(true);
    try {
      console.log('📄 Starting PDF export...');

      // Fetch ALL referrals for export (no pagination limits)
      const params = new URLSearchParams({
        search: searchQuery,
        status: statusFilter,
        pageSize: '999999',
        exportAll: 'true',
        useUserModel: 'false' // Try referral model first
      });

      const response = await fetch(`/api/admin/referrals?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📊 PDF EXPORT - API Response:', {
          success: data.success,
          totalReferrals: data.totalReferrals,
          actualCount: data.actualCount,
          isExportMode: data.isExportMode,
          dataSource: data.dataSource,
          referralsLength: data.referrals?.length
        });

        if (!data.success) {
          throw new Error(data.error || 'API returned unsuccessful response');
        }

        if (data.referrals.length === 0) {
          toast.error('No referrals found to export');
          return;
        }

        console.log(`✅ SUCCESS: Got ${data.referrals.length} records for PDF export`);

        // Prepare referrals for export
        const exportReferrals = data.referrals.map((referral: any) => ({
          _id: referral._id,
          referrer: {
            name: referral.referrer?.name || 'N/A',
            email: referral.referrer?.email || 'N/A'
          },
          referredUser: referral.referredUser ? {
            name: referral.referredUser.name || 'N/A',
            email: referral.referredUser.email || 'N/A'
          } : null,
          referralId: referral.referralId,
          createdAt: referral.createdAt,
          active: referral.active,
          earnings: referral.earnings || []
        }));

        console.log('📄 Attempting PDF export with', exportReferrals.length, 'referrals');

        // Use the comprehensive PDF export
        try {
          downloadReferralsPDF(exportReferrals, {
            search: searchQuery,
            status: statusFilter
          });
          console.log('✅ PDF export function completed');
          toast.success(`PDF export completed! ${exportReferrals.length} referrals exported 📄`);
        } catch (pdfError) {
          console.error('❌ PDF generation error:', pdfError);
          throw new Error(`PDF generation failed: ${(pdfError as Error).message}`);
        }
      } else {
        const errorData = await response.json();
        console.error('❌ Export API failed:', errorData);
        throw new Error(errorData.error || 'Export API failed');
      }
    } catch (error) {
      console.error('❌ Error exporting PDF:', error);
      toast.error(`PDF export failed: ${(error as Error).message}`);
    } finally {
      setExporting(false);
    }
  };

  const exportToExcel = async () => {
    setExporting(true);
    try {
      console.log('📊 Starting Excel export...');

      // Fetch ALL referrals for export (no pagination limits)
      const params = new URLSearchParams({
        search: searchQuery,
        status: statusFilter,
        pageSize: '999999',
        exportAll: 'true',
        useUserModel: 'false' // Try referral model first
      });

      const response = await fetch(`/api/admin/referrals?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('📊 EXCEL EXPORT - API Response:', {
          success: data.success,
          totalReferrals: data.totalReferrals,
          actualCount: data.actualCount,
          isExportMode: data.isExportMode,
          dataSource: data.dataSource,
          referralsLength: data.referrals?.length
        });

        if (!data.success) {
          throw new Error(data.error || 'API returned unsuccessful response');
        }

        if (data.referrals.length === 0) {
          toast.error('No referrals found to export');
          return;
        }

        console.log(`✅ SUCCESS: Got ${data.referrals.length} records for Excel export`);

        // Prepare referrals for export
        const exportReferrals = data.referrals.map((referral: any) => ({
          _id: referral._id,
          referrer: {
            name: referral.referrer?.name || 'N/A',
            email: referral.referrer?.email || 'N/A'
          },
          referredUser: referral.referredUser ? {
            name: referral.referredUser.name || 'N/A',
            email: referral.referredUser.email || 'N/A'
          } : null,
          referralId: referral.referralId,
          createdAt: referral.createdAt,
          active: referral.active,
          earnings: referral.earnings || []
        }));

        console.log('📊 Attempting Excel export with', exportReferrals.length, 'referrals');

        // Use the comprehensive Excel export
        try {
          downloadReferralsExcel(exportReferrals, {
            search: searchQuery,
            status: statusFilter
          });
          console.log('✅ Excel export function completed');
          toast.success(`Excel export completed! ${exportReferrals.length} referrals exported 📊`);
        } catch (excelError) {
          console.error('❌ Excel generation error:', excelError);
          throw new Error(`Excel generation failed: ${(excelError as Error).message}`);
        }
      } else {
        const errorData = await response.json();
        console.error('❌ Export API failed:', errorData);
        throw new Error(errorData.error || 'Export API failed');
      }
    } catch (error) {
      console.error('❌ Error exporting Excel:', error);
      toast.error(`Excel export failed: ${(error as Error).message}`);
    } finally {
      setExporting(false);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getBadgeVariant = (active: boolean) => {
    return active 
      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
      : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Export Options */}
      <div className="flex items-center justify-between p-6 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 rounded-t-xl border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/30 rounded-lg">
            <Users className="h-5 w-5 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Referral Database
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {referrals.length} referrals • Real-time data
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {/* Bulk Actions */}
          {selectedItems.length > 0 && (
            <div className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-800 shadow-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-semibold text-blue-700 dark:text-blue-300">
                  {selectedItems.length} referral{selectedItems.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <div className="h-4 w-px bg-blue-300 dark:bg-blue-600"></div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkDelete}
                disabled={deleting || selectedItems.length === 0}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                {deleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-1"></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete Selected
                  </>
                )}
              </Button>
            </div>
          )}
          
          {/* Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                disabled={exporting}
                className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700 shadow-sm"
              >
                {exporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600 mr-2"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
              <DropdownMenuLabel className="text-slate-700 dark:text-slate-300">
                Export Options
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
              <DropdownMenuItem
                onClick={exportToPDF}
                disabled={exporting}
                className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800"
              >
                <FileText className="h-4 w-4 mr-2 text-red-500" />
                <div className="flex-1">
                  <div className="font-medium">Export as PDF</div>
                  <div className="text-xs text-slate-500">Professional report format</div>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={exportToExcel}
                disabled={exporting}
                className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800"
              >
                <FileSpreadsheet className="h-4 w-4 mr-2 text-green-500" />
                <div className="flex-1">
                  <div className="font-medium">Export as Excel</div>
                  <div className="text-xs text-slate-500">Spreadsheet with analytics</div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="px-6">
        {/* Enhanced Filters and Search */}
        <div className="space-y-4 mb-6">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              type="text"
              placeholder="Search referrals by referrer, referred user, or referral ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-3 bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 focus:border-purple-500 dark:focus:border-purple-400 rounded-xl shadow-sm"
            />
          </div>

          {/* Filter Controls */}
          <div className="flex flex-wrap items-center gap-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Filters:</span>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>

            <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
              <SelectTrigger className="w-[100px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            {(searchQuery || statusFilter !== 'all') && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('all');
                }}
                className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
              >
                Clear filters
              </Button>
            )}
          </div>
        </div>

        {/* Modern Referral Table */}
        <div className="rounded-xl border border-slate-200 dark:border-slate-700 overflow-hidden bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm">
          <Table>
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 border-b border-slate-200 dark:border-slate-600">
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">
                  <input
                    type="checkbox"
                    checked={selectedItems.length === referrals.length && referrals.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="mr-2"
                  />
                  Select All
                </TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">
                  <div className="flex items-center space-x-1">
                    <Users className="h-4 w-4" />
                    <span>Referrer</span>
                  </div>
                </TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Referred User</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Referral ID</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Earnings</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Status</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Date Created</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: pageSize }).map((_, index) => (
                    <TableRow key={index} className="border-b border-slate-100 dark:border-slate-800">
                      <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                    </TableRow>
                  ))
                : referrals.length > 0
                ? referrals.map((referral, index) => (
                    <TableRow
                      key={referral._id}
                      className={`group border-b border-slate-100 dark:border-slate-800 hover:bg-gradient-to-r hover:from-purple-50/50 hover:to-blue-50/50 dark:hover:from-purple-900/10 dark:hover:to-blue-900/10 transition-all duration-200 ${
                        index % 2 === 0 ? 'bg-white dark:bg-slate-900' : 'bg-slate-50/50 dark:bg-slate-800/50'
                      }`}
                    >
                      <TableCell className="py-4">
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(referral._id)}
                            onChange={(e) => handleSelectItem(referral._id, e.target.checked)}
                          />
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-purple-500 to-blue-600 text-white font-semibold flex items-center justify-center">
                            {referral.referrer?.name?.charAt(0) || 'R'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div>
                          <div className="font-semibold text-slate-900 dark:text-white group-hover:text-purple-700 dark:group-hover:text-purple-300 transition-colors">
                            {referral.referrer?.name || 'N/A'}
                          </div>
                          <div className="text-sm text-slate-500 dark:text-slate-400 font-mono">
                            {referral.referrer?.email || 'N/A'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div>
                          <div className="font-medium text-slate-900 dark:text-white">
                            {referral.referredUser?.name || 'Pending'}
                          </div>
                          <div className="text-sm text-slate-500 dark:text-slate-400 font-mono">
                            {referral.referredUser?.email || 'No user yet'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="font-mono text-sm bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded">
                          {referral.referralId}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div>
                          {referral.earnings && referral.earnings.length > 0 ? (
                            <>
                              <div className="font-semibold text-slate-900 dark:text-white">
                                ${referral.earnings.reduce((sum, earning) => sum + earning.earningAmount, 0).toLocaleString()}
                              </div>
                              <div className="text-xs text-green-600 dark:text-green-400">
                                {referral.earnings.length} earning{referral.earnings.length !== 1 ? 's' : ''}
                              </div>
                            </>
                          ) : (
                            <div className="text-sm text-slate-500 dark:text-slate-400">No earnings</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge className={`font-medium ${getBadgeVariant(referral.active)}`}>
                          {referral.active ? 'ACTIVE' : 'INACTIVE'}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="text-sm text-slate-700 dark:text-slate-300">
                          {new Date(referral.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              className="h-8 w-8 p-0 hover:bg-slate-100 dark:hover:bg-slate-800 group-hover:bg-purple-100 dark:group-hover:bg-purple-900/30 transition-all"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            align="end"
                            className="w-56 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700"
                          >
                            <DropdownMenuLabel className="text-slate-700 dark:text-slate-300">
                              Referral Actions
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
                            <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800">
                              <Eye className="h-4 w-4 mr-2 text-blue-500" />
                              <div>
                                <div className="font-medium">View Details</div>
                                <div className="text-xs text-slate-500">Complete referral info</div>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800">
                              <Edit className="h-4 w-4 mr-2 text-green-500" />
                              <div>
                                <div className="font-medium">Edit Referral</div>
                                <div className="text-xs text-slate-500">Update referral details</div>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
                            <DropdownMenuItem className="text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 focus:bg-red-50 dark:focus:bg-red-900/20">
                              <Trash2 className="h-4 w-4 mr-2" />
                              <div>
                                <div className="font-medium">Delete Referral</div>
                                <div className="text-xs text-red-500">Permanently remove</div>
                              </div>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-12">
                      <div className="flex flex-col items-center space-y-4">
                        <div className="p-4 bg-slate-100 dark:bg-slate-800 rounded-full">
                          <Users className="h-8 w-8 text-slate-400" />
                        </div>
                        <div>
                          <p className="text-lg font-medium text-slate-900 dark:text-white">No referrals found</p>
                          <p className="text-slate-500 dark:text-slate-400">Try adjusting your search or filter criteria</p>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>

        {/* Enhanced Pagination */}
        <div className="flex items-center justify-between p-6 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 border-t border-slate-200 dark:border-slate-700 rounded-b-xl">
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Showing <span className="font-bold text-purple-600">{referrals.length}</span> of{' '}
              <span className="font-bold text-purple-600">{totalPages * pageSize}</span> referrals
            </div>
            {(searchQuery || statusFilter !== 'all') && (
              <div className="text-xs text-slate-500 dark:text-slate-400 bg-purple-50 dark:bg-purple-900/20 px-2 py-1 rounded-md">
                Filtered results
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                if (pageNum > totalPages) return null;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={`w-8 h-8 p-0 ${
                      currentPage === pageNum
                        ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600'
                        : 'bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700'
                    }`}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModernReferralTable;
