

// components/admin/SubscriptionTable.tsx

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
// import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ArrowUpDown,
  Search,
  Download,
  FileText,
  FileSpreadsheet,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  TrendingUp,
  DollarSign,
  Filter,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { SubscriptionDetailsOverlay } from './SubscriptionDetailsOverlay';
import { downloadInvestmentsPDF, downloadInvestmentsExcel } from '@/lib/investmentExport';
import toast from 'react-hot-toast';

interface Subscription {
  _id: string;
  userId: { name: string; email: string };
  plan: { title: string; percentage: number };
  startDate: string;
  endDate: string;
  amount: number;
  status: 'active' | 'inactive' | 'archived';
  duration?: string;
  roi?: number;
}

const SubscriptionTable: React.FC = () => {
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortColumn, setSortColumn] = useState<string | null>(null);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedSubscription, setSelectedSubscription] = useState<Subscription | null>(null);
  const [isOverlayOpen, setIsOverlayOpen] = useState(false);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [exporting, setExporting] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    fetchSubscriptions();
  }, [searchQuery, sortColumn, sortOrder, statusFilter, planFilter, currentPage, pageSize]);

  const fetchSubscriptions = async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/subscriptions/admin?search=${searchQuery}&sort=${sortColumn}&order=${sortOrder}&status=${statusFilter}&plan=${planFilter}&page=${currentPage}&pageSize=${pageSize}`
      );
      const data = await response.json();
      setSubscriptions(data.subscriptions || []);
      setTotalPages(data.totalPages || 1);
    } catch (error) {
      console.error('Error fetching subscriptions:', error);
      // Fallback data for development
      const mockData = generateMockSubscriptions();
      setSubscriptions(mockData);
      setTotalPages(Math.ceil(mockData.length / pageSize));
    } finally {
      setLoading(false);
    }
  };

  // Mock data generator for development
  const generateMockSubscriptions = (): Subscription[] => {
    return Array.from({ length: 50 }, (_, i) => ({
      _id: `sub_${i + 1}`,
      userId: {
        name: `User ${i + 1}`,
        email: `user${i + 1}@example.com`
      },
      plan: {
        title: ['Basic Plan', 'Premium Plan', 'Enterprise Plan'][i % 3],
        percentage: [5, 10, 15][i % 3]
      },
      startDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      endDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      amount: Math.floor(Math.random() * 100000) + 5000,
      status: ['active', 'inactive', 'archived'][i % 3] as 'active' | 'inactive' | 'archived',
      duration: ['6 months', '12 months', '24 months'][i % 3],
      roi: Math.floor(Math.random() * 30) + 5
    }));
  };

  const handleSort = (column: string) => {
    setSortOrder(sortColumn === column && sortOrder === 'asc' ? 'desc' : 'asc');
    setSortColumn(column);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(subscriptions.map(sub => sub._id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, id]);
    } else {
      setSelectedItems(prev => prev.filter(item => item !== id));
    }
  };

  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    const confirmMessage = `⚠️ DELETE CONFIRMATION\n\nYou are about to permanently delete ${selectedItems.length} investment(s).\n\nThis action cannot be undone and will remove:\n• Investment records\n• Associated data\n• Transaction history\n\nAre you sure you want to proceed?`;

    if (confirm(confirmMessage)) {
      setDeleting(true);

      try {
        console.log('Deleting investments:', selectedItems);

        const response = await fetch('/api/subscriptions/bulk-delete', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ ids: selectedItems })
        });

        const data = await response.json();

        if (response.ok) {
          // Remove deleted items from local state immediately for better UX
          setSubscriptions(prev => prev.filter(sub => !selectedItems.includes(sub._id)));
          setSelectedItems([]);

          // Show success message
          const successMessage = `✅ SUCCESS\n\nSuccessfully deleted ${data.deletedCount} investment(s).\n\nThe investment records have been permanently removed from the system.`;
          alert(successMessage);

          // Refresh the data to ensure consistency
          await fetchSubscriptions();
        } else {
          console.error('Delete failed:', data);
          const errorMessage = `❌ DELETE FAILED\n\n${data.error || 'Unknown error occurred'}\n\nPlease try again or contact support if the problem persists.`;
          alert(errorMessage);
        }
      } catch (error) {
        console.error('Error deleting investments:', error);
        const networkErrorMessage = `🌐 NETWORK ERROR\n\nFailed to connect to the server.\n\nPlease check your internet connection and try again.`;
        alert(networkErrorMessage);
      } finally {
        setDeleting(false);
      }
    }
  };

  const exportToPDF = async () => {
    setExporting(true);
    try {
      // Fetch all investments for export (not paginated)
      const params = new URLSearchParams({
        search: searchQuery,
        status: statusFilter,
        plan: planFilter,
        pageSize: '1000' // Get all investments for export
      });

      const response = await fetch(`/api/subscriptions/admin?${params}`);
      if (response.ok) {
        const data = await response.json();
        console.log('API Response:', {
          totalSubscriptions: data.totalSubscriptions,
          actualCount: data.actualCount,
          isExportMode: data.isExportMode,
          subscriptionsLength: data.subscriptions?.length
        });

        if (!data.isExportMode) {
          console.warn('WARNING: Not in export mode! Only got paginated data.');
        }

        const exportInvestments = data.subscriptions.map((investment: any) => ({
          _id: investment._id,
          userId: {
            name: investment.userId?.name || 'N/A',
            email: investment.userId?.email || 'N/A'
          },
          plan: {
            title: investment.plan?.title || 'N/A',
            percentage: investment.plan?.percentage || 0
          },
          startDate: investment.startDate,
          endDate: investment.endDate,
          amount: investment.amount,
          status: investment.status,
          duration: investment.duration || calculateDuration(investment.startDate, investment.endDate),
          roi: investment.roi || investment.plan?.percentage || 0,
          createdAt: investment.createdAt,
          updatedAt: investment.updatedAt
        }));

        console.log('Attempting PDF export with', exportInvestments.length, 'investments');
        console.log('Total in database:', data.totalSubscriptions);
        console.log('Actually fetched:', data.actualCount);
        console.log('Sample investment data:', exportInvestments[0]);

        // Use the comprehensive PDF export with error handling
        try {
          downloadInvestmentsPDF(exportInvestments, {
            search: searchQuery,
            status: statusFilter,
            plan: planFilter
          });
          console.log('PDF export function completed');
        } catch (pdfError) {
          console.error('PDF generation error:', pdfError);
          throw new Error(`PDF generation failed: ${(pdfError as Error).message}`);
        }

        // Show success message
        toast.success('PDF export completed successfully! 📄');
      } else {
        console.error('Export failed:', response.status, response.statusText);
        toast.error('PDF export failed. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting PDF:', error);
      toast.error('PDF export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  const exportToExcel = async () => {
    setExporting(true);
    try {
      // Fetch all investments for export (not paginated)
      const params = new URLSearchParams({
        search: searchQuery,
        status: statusFilter,
        plan: planFilter,
        pageSize: '1000' // Get all investments for export
      });

      const response = await fetch(`/api/subscriptions/admin?${params}`);
      if (response.ok) {
        const data = await response.json();
        console.log('Excel API Response:', {
          totalSubscriptions: data.totalSubscriptions,
          actualCount: data.actualCount,
          isExportMode: data.isExportMode,
          subscriptionsLength: data.subscriptions?.length
        });

        if (!data.isExportMode) {
          console.warn('WARNING: Excel export not in export mode! Only got paginated data.');
        }

        const exportInvestments = data.subscriptions.map((investment: any) => ({
          _id: investment._id,
          userId: {
            name: investment.userId?.name || 'N/A',
            email: investment.userId?.email || 'N/A'
          },
          plan: {
            title: investment.plan?.title || 'N/A',
            percentage: investment.plan?.percentage || 0
          },
          startDate: investment.startDate,
          endDate: investment.endDate,
          amount: investment.amount,
          status: investment.status,
          duration: investment.duration || calculateDuration(investment.startDate, investment.endDate),
          roi: investment.roi || investment.plan?.percentage || 0,
          createdAt: investment.createdAt,
          updatedAt: investment.updatedAt
        }));

        console.log('Attempting Excel export with', exportInvestments.length, 'investments');
        console.log('Total in database:', data.totalSubscriptions);
        console.log('Actually fetched:', data.actualCount);

        // Use the comprehensive Excel export with error handling
        try {
          downloadInvestmentsExcel(exportInvestments, {
            search: searchQuery,
            status: statusFilter,
            plan: planFilter
          });
          console.log('Excel export function completed');
        } catch (excelError) {
          console.error('Excel generation error:', excelError);
          throw new Error(`Excel generation failed: ${(excelError as Error).message}`);
        }

        // Show success message
        toast.success('Excel export completed successfully! 📊');
      } else {
        console.error('Export failed:', response.status, response.statusText);
        toast.error('Excel export failed. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting Excel:', error);
      toast.error('Excel export failed. Please try again.');
    } finally {
      setExporting(false);
    }
  };

  // Helper function to calculate duration
  const calculateDuration = (startDate: string, endDate: string): string => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    const diffMonths = Math.floor(diffDays / 30);

    if (diffMonths < 1) {
      return `${diffDays} days`;
    } else if (diffMonths < 12) {
      return `${diffMonths} months`;
    } else {
      const years = Math.floor(diffMonths / 12);
      const remainingMonths = diffMonths % 12;
      return remainingMonths > 0 ? `${years}y ${remainingMonths}m` : `${years} years`;
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const getBadgeVariant = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400';
      case 'inactive':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400';
      case 'archived':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400';
      default:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400';
    }
  };

  const renderSkeletonRow = () => (
    <TableRow>
      <TableCell>
        <Skeleton className="h-4 w-[250px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[100px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[60px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
      <TableCell>
        <Skeleton className="h-4 w-[80px]" />
      </TableCell>
    </TableRow>
  );

  const handleViewClick = (subscription: Subscription) => {
    setSelectedSubscription(subscription);
    setIsOverlayOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Export Options */}
      <div className="flex items-center justify-between p-6 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 rounded-t-xl border-b border-slate-200 dark:border-slate-700">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
            <TrendingUp className="h-5 w-5 text-green-600 dark:text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-white">
              Investment Database
            </h3>
            <p className="text-sm text-slate-600 dark:text-slate-400">
              {subscriptions.length} investments • Real-time data
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-3">
          {/* Bulk Actions */}
          {selectedItems.length > 0 && (
            <div className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 px-4 py-2 rounded-lg border border-blue-200 dark:border-blue-800 shadow-sm">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-semibold text-blue-700 dark:text-blue-300">
                  {selectedItems.length} investment{selectedItems.length !== 1 ? 's' : ''} selected
                </span>
              </div>
              <div className="h-4 w-px bg-blue-300 dark:bg-blue-600"></div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleBulkDelete}
                disabled={deleting || selectedItems.length === 0}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
              >
                {deleting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-1"></div>
                    Deleting...
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-1" />
                    Delete Selected
                  </>
                )}
              </Button>
            </div>
          )}

          {/* Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                disabled={exporting}
                className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700 shadow-sm"
              >
                {exporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Export Data
                  </>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
              <DropdownMenuLabel className="text-slate-700 dark:text-slate-300">
                Export Options
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
              <DropdownMenuItem
                onClick={exportToPDF}
                disabled={exporting}
                className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800"
              >
                <FileText className="h-4 w-4 mr-2 text-red-500" />
                <div className="flex-1">
                  <div className="font-medium">Export as PDF</div>
                  <div className="text-xs text-slate-500">Professional report format</div>
                </div>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={exportToExcel}
                disabled={exporting}
                className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800"
              >
                <FileSpreadsheet className="h-4 w-4 mr-2 text-green-500" />
                <div className="flex-1">
                  <div className="font-medium">Export as Excel</div>
                  <div className="text-xs text-slate-500">Spreadsheet with analytics</div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      <div className="px-6">
        {/* Enhanced Filters and Search */}
        <div className="space-y-4 mb-6">
          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              type="text"
              placeholder="Search investments by user, plan, or amount..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-4 py-3 bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 focus:border-green-500 dark:focus:border-green-400 rounded-xl shadow-sm"
            />
          </div>

          {/* Filter Controls */}
          <div className="flex flex-wrap items-center gap-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-slate-700 dark:text-slate-300">Filters:</span>
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[140px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
                <SelectItem value="archived">Archived</SelectItem>
              </SelectContent>
            </Select>

            <Select value={planFilter} onValueChange={setPlanFilter}>
              <SelectTrigger className="w-[140px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue placeholder="Plan" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="all">All Plans</SelectItem>
                <SelectItem value="basic">Basic Plan</SelectItem>
                <SelectItem value="premium">Premium Plan</SelectItem>
                <SelectItem value="enterprise">Enterprise Plan</SelectItem>
              </SelectContent>
            </Select>

            <Select value={pageSize.toString()} onValueChange={(value) => setPageSize(Number(value))}>
              <SelectTrigger className="w-[100px] bg-white/80 dark:bg-slate-800/80 border-slate-200 dark:border-slate-700 rounded-lg">
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700">
                <SelectItem value="10">10</SelectItem>
                <SelectItem value="25">25</SelectItem>
                <SelectItem value="50">50</SelectItem>
                <SelectItem value="100">100</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            {(searchQuery || statusFilter !== 'all' || planFilter !== 'all') && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setSearchQuery('');
                  setStatusFilter('all');
                  setPlanFilter('all');
                }}
                className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
              >
                Clear filters
              </Button>
            )}
          </div>
        </div>

        <div className="rounded-xl border border-slate-200 dark:border-slate-700 overflow-hidden bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm">
          <Table>
            <TableHeader>
              <TableRow className="bg-gradient-to-r from-slate-50 to-slate-100 dark:from-slate-800 dark:to-slate-700 border-b border-slate-200 dark:border-slate-600">
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">
                  <input
                    type="checkbox"
                    checked={selectedItems.length === subscriptions.length && subscriptions.length > 0}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="mr-2"
                  />
                  Select All
                </TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">
                  <div className="flex items-center space-x-1">
                    <DollarSign className="h-4 w-4" />
                    <span>Investor</span>
                  </div>
                </TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Investment Plan</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Amount & ROI</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Duration</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Status</TableHead>
                <TableHead className="font-semibold text-slate-700 dark:text-slate-300">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading
                ? Array.from({ length: pageSize }).map((_, index) => (
                    <TableRow key={index} className="border-b border-slate-100 dark:border-slate-800">
                      <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[150px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[120px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[100px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                      <TableCell><Skeleton className="h-4 w-[80px]" /></TableCell>
                    </TableRow>
                  ))
                : subscriptions.length > 0
                ? subscriptions.map((subscription, index) => (
                    <TableRow
                      key={subscription._id}
                      className={`group border-b border-slate-100 dark:border-slate-800 hover:bg-gradient-to-r hover:from-green-50/50 hover:to-blue-50/50 dark:hover:from-green-900/10 dark:hover:to-blue-900/10 transition-all duration-200 ${
                        index % 2 === 0 ? 'bg-white dark:bg-slate-900' : 'bg-slate-50/50 dark:bg-slate-800/50'
                      }`}
                    >
                      <TableCell className="py-4">
                        <div className="flex items-center space-x-3">
                          <input
                            type="checkbox"
                            checked={selectedItems.includes(subscription._id)}
                            onChange={(e) => handleSelectItem(subscription._id, e.target.checked)}
                          />
                          <div className="h-10 w-10 rounded-full bg-gradient-to-br from-green-500 to-blue-600 text-white font-semibold flex items-center justify-center">
                            {subscription.userId?.name?.charAt(0) || 'U'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div>
                          <div className="font-semibold text-slate-900 dark:text-white group-hover:text-green-700 dark:group-hover:text-green-300 transition-colors">
                            {subscription.userId?.name || 'N/A'}
                          </div>
                          <div className="text-sm text-slate-500 dark:text-slate-400 font-mono">
                            {subscription.userId?.email || 'N/A'}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div>
                          <div className="font-medium text-slate-900 dark:text-white">
                            {subscription.plan?.title || 'N/A'}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">
                            {subscription.plan?.percentage || 0}% ROI
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div>
                          <div className="font-semibold text-slate-900 dark:text-white">
                            ${subscription.amount.toLocaleString()}
                          </div>
                          <div className="text-xs text-green-600 dark:text-green-400">
                            +{subscription.roi || subscription.plan?.percentage || 0}% expected
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <div className="text-sm">
                          <div className="text-slate-700 dark:text-slate-300">
                            {subscription.duration || 'N/A'}
                          </div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">
                            {new Date(subscription.startDate).toLocaleDateString()} - {new Date(subscription.endDate).toLocaleDateString()}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4">
                        <Badge className={`font-medium ${getBadgeVariant(subscription.status)}`}>
                          {subscription.status.toUpperCase()}
                        </Badge>
                      </TableCell>
                      <TableCell className="py-4">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              className="h-8 w-8 p-0 hover:bg-slate-100 dark:hover:bg-slate-800 group-hover:bg-green-100 dark:group-hover:bg-green-900/30 transition-all"
                            >
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent
                            align="end"
                            className="w-56 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700"
                          >
                            <DropdownMenuLabel className="text-slate-700 dark:text-slate-300">
                              Investment Actions
                            </DropdownMenuLabel>
                            <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
                            <DropdownMenuItem
                              onClick={() => handleViewClick(subscription)}
                              className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800"
                            >
                              <Eye className="h-4 w-4 mr-2 text-blue-500" />
                              <div>
                                <div className="font-medium">View Details</div>
                                <div className="text-xs text-slate-500">Complete investment info</div>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800 focus:bg-slate-100 dark:focus:bg-slate-800">
                              <Edit className="h-4 w-4 mr-2 text-green-500" />
                              <div>
                                <div className="font-medium">Edit Investment</div>
                                <div className="text-xs text-slate-500">Update investment details</div>
                              </div>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />
                            <DropdownMenuItem className="text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 focus:bg-red-50 dark:focus:bg-red-900/20">
                              <Trash2 className="h-4 w-4 mr-2" />
                              <div>
                                <div className="font-medium">Delete Investment</div>
                                <div className="text-xs text-red-500">Permanently remove</div>
                              </div>
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                : (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-12">
                      <div className="flex flex-col items-center space-y-4">
                        <div className="p-4 bg-slate-100 dark:bg-slate-800 rounded-full">
                          <TrendingUp className="h-8 w-8 text-slate-400" />
                        </div>
                        <div>
                          <p className="text-lg font-medium text-slate-900 dark:text-white">No investments found</p>
                          <p className="text-slate-500 dark:text-slate-400">Try adjusting your search or filter criteria</p>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
            </TableBody>
          </Table>
        </div>

        {/* Enhanced Pagination */}
        <div className="flex items-center justify-between p-6 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 border-t border-slate-200 dark:border-slate-700 rounded-b-xl">
          <div className="flex items-center space-x-2">
            <div className="text-sm font-medium text-slate-700 dark:text-slate-300">
              Showing <span className="font-bold text-green-600">{subscriptions.length}</span> of{' '}
              <span className="font-bold text-green-600">{totalPages * pageSize}</span> investments
            </div>
            {(searchQuery || statusFilter !== 'all' || planFilter !== 'all') && (
              <div className="text-xs text-slate-500 dark:text-slate-400 bg-green-50 dark:bg-green-900/20 px-2 py-1 rounded-md">
                Filtered results
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                if (pageNum > totalPages) return null;
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => handlePageChange(pageNum)}
                    className={`w-8 h-8 p-0 ${
                      currentPage === pageNum
                        ? 'bg-green-600 hover:bg-green-700 text-white border-green-600'
                        : 'bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700'
                    }`}
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
              className="bg-white/80 hover:bg-white dark:bg-slate-800/80 dark:hover:bg-slate-800 border-slate-200 dark:border-slate-700"
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <SubscriptionDetailsOverlay
          isOpen={isOverlayOpen}
          onClose={() => setIsOverlayOpen(false)}
          subscription={selectedSubscription}
        />
      </div>
    </div>
  );
};

export default SubscriptionTable;
