// // components/admin/AdminTopNavbar.tsx
// 'use client';

// import React from 'react';
// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/navigation';

// const AdminTopNavbar: React.FC = () => {
//   const { user, logout } = useAuth();
//   const router = useRouter();

//   const handleLogout = () => {
//     logout();
//     router.push('/'); // Redirect to home page after logout
//   };

//   return (
//     <header className="bg-white shadow-sm flex items-center justify-between px-6 py-4">
//       <div>
//         {/* Placeholder for any left-side content */}
//         <h1 className="text-xl font-semibold text-gray-800">Admin Dashboard</h1>
//       </div>
//       <div className="flex items-center space-x-4">
//         <span className="text-gray-800 font-medium">Hello, {user?.name}</span>
//         <button
//           onClick={handleLogout}
//           className="px-3 py-1 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 transition-colors duration-200"
//         >
//           Logout
//         </button>
//       </div>
//     </header>
//   );
// };

// export default AdminTopNavbar;


// // components/admin/AdminTopNavbar.tsx
// 'use client';

// import React from 'react';
// import { useAuth } from '@/context/AuthContext';
// import { useRouter } from 'next/navigation';
// import ThemeSwitcher from '@/components/ThemeSwitcher';

// const AdminTopNavbar: React.FC = () => {
//   const { user, logout } = useAuth();
//   const router = useRouter();

//   const handleLogout = () => {
//     logout();
//     router.push('/'); // Redirect to home page after logout
//   };

//   return (
//     <header className="bg-white dark:bg-gray-900 shadow-sm flex items-center justify-between px-6 py-4">
//       <div>
//         <h1 className="text-xl font-semibold text-gray-800 dark:text-gray-200">Admin Dashboard</h1>
//       </div>
//       <div className="flex items-center space-x-4">
//         <span className="text-gray-800 dark:text-gray-200 font-medium">Hello, {user?.name}</span>
//         <ThemeSwitcher />
//         <button
//           onClick={handleLogout}
//           className="px-3 py-1 rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700 dark:bg-red-700 dark:hover:bg-red-800 transition-colors duration-200"
//         >
//           Logout
//         </button>
//       </div>
//     </header>
//   );
// };

// export default AdminTopNavbar;


'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter, usePathname } from 'next/navigation';
import ThemeSwitcher from '@/components/ThemeSwitcher';
import {
  Menu,
  Bell,
  Search,
  Settings,
  User,
  LogOut,
  ChevronDown,
  Shield,
  Activity,
  MessageSquare,
  HelpCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';

interface AdminTopNavbarProps {
  toggleSidebar: () => void;
}

const AdminTopNavbar: React.FC<AdminTopNavbarProps> = ({ toggleSidebar }) => {
  const { user, logout } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const handleLogout = () => {
    logout();
    router.push('/');
  };

  // Get page title based on current route
  const getPageTitle = () => {
    if (pathname.includes('/all-investors')) return 'Members & Investors';
    if (pathname.includes('/all-investments')) return 'Investments';
    if (pathname.includes('/all-referrals')) return 'Referrals';
    if (pathname.includes('/payouts')) return 'Payouts';
    if (pathname.includes('/plans')) return 'Investment Plans';
    if (pathname.includes('/analytics')) return 'Analytics';
    if (pathname.includes('/settings')) return 'Settings';
    return 'Dashboard';
  };

  return (
    <header className="sticky top-0 z-30 bg-white/80 dark:bg-slate-900/80 backdrop-blur-md border-b border-slate-200 dark:border-slate-700 shadow-sm">
      <div className="flex items-center justify-between px-6 py-4">
        {/* Left Section */}
        <div className="flex items-center space-x-4">
          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden p-2 hover:bg-slate-100 dark:hover:bg-slate-800"
            onClick={toggleSidebar}
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Page Title & Breadcrumb */}
          <div className="flex items-center space-x-3">
            <div className="hidden md:block">
              <h1 className="text-xl font-bold text-slate-900 dark:text-white">
                {getPageTitle()}
              </h1>
              <div className="flex items-center space-x-1 text-sm text-slate-500 dark:text-slate-400">
                <span>Admin</span>
                <span>/</span>
                <span className="text-blue-600 dark:text-blue-400">{getPageTitle()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Center Section - Search */}
        <div className="hidden lg:flex flex-1 max-w-md mx-8">
          <div className="relative w-full">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <input
              type="text"
              placeholder="Search members, investments, or transactions..."
              className="w-full pl-10 pr-4 py-2 bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
            />
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-3">
          {/* Quick Actions */}
          <div className="hidden md:flex items-center space-x-2">
            {/* Notifications */}
            <Button variant="ghost" size="sm" className="relative p-2 hover:bg-slate-100 dark:hover:bg-slate-800">
              <Bell className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 bg-red-500 text-white text-xs flex items-center justify-center">
                3
              </Badge>
            </Button>

            {/* Messages */}
            <Button variant="ghost" size="sm" className="relative p-2 hover:bg-slate-100 dark:hover:bg-slate-800">
              <MessageSquare className="h-5 w-5" />
              <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 bg-blue-500 text-white text-xs flex items-center justify-center">
                2
              </Badge>
            </Button>

            {/* Help */}
            <Button variant="ghost" size="sm" className="p-2 hover:bg-slate-100 dark:hover:bg-slate-800">
              <HelpCircle className="h-5 w-5" />
            </Button>
          </div>

          {/* Theme Switcher */}
          <ThemeSwitcher />

          {/* User Profile Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-3 p-2 hover:bg-slate-100 dark:hover:bg-slate-800 rounded-xl">
                <Avatar className="h-8 w-8 ring-2 ring-slate-200 dark:ring-slate-700">
                  <AvatarImage src={user?.profilePicture} alt={user?.name} />
                  <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                    {user?.name?.charAt(0) || 'A'}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium text-slate-900 dark:text-white">
                    {user?.name || 'Admin'}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400 flex items-center">
                    <Shield className="h-3 w-3 mr-1" />
                    Administrator
                  </div>
                </div>
                <ChevronDown className="h-4 w-4 text-slate-400" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-64 bg-white/95 dark:bg-slate-900/95 backdrop-blur-sm border-slate-200 dark:border-slate-700"
            >
              <DropdownMenuLabel className="text-slate-700 dark:text-slate-300">
                <div className="flex items-center space-x-2">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user?.profilePicture} alt={user?.name} />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                      {user?.name?.charAt(0) || 'A'}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">{user?.name || 'Admin'}</div>
                    <div className="text-xs text-slate-500">{user?.email}</div>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />

              <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800">
                <User className="h-4 w-4 mr-2" />
                <div>
                  <div className="font-medium">Profile Settings</div>
                  <div className="text-xs text-slate-500">Manage your account</div>
                </div>
              </DropdownMenuItem>

              <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800">
                <Settings className="h-4 w-4 mr-2" />
                <div>
                  <div className="font-medium">System Settings</div>
                  <div className="text-xs text-slate-500">Configure admin panel</div>
                </div>
              </DropdownMenuItem>

              <DropdownMenuItem className="hover:bg-slate-100 dark:hover:bg-slate-800">
                <Activity className="h-4 w-4 mr-2" />
                <div>
                  <div className="font-medium">Activity Log</div>
                  <div className="text-xs text-slate-500">View recent actions</div>
                </div>
              </DropdownMenuItem>

              <DropdownMenuSeparator className="bg-slate-200 dark:bg-slate-700" />

              <DropdownMenuItem
                onClick={handleLogout}
                className="text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 focus:bg-red-50 dark:focus:bg-red-900/20"
              >
                <LogOut className="h-4 w-4 mr-2" />
                <div>
                  <div className="font-medium">Sign Out</div>
                  <div className="text-xs text-red-500">Logout from admin panel</div>
                </div>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default AdminTopNavbar;

