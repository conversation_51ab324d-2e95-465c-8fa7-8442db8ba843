


// // components/admin/AdminSidebar.tsx
// 'use client';

// import React from 'react';
// import { usePathname, useRouter } from 'next/navigation';
// import { motion } from 'framer-motion';
// import { useAuth } from '@/context/AuthContext';
// import {
//   FaTachometerAlt,
//   FaUsers,
//   FaChartLine,
//   FaUserFriends,
//   FaMoneyCheckAlt,
//   FaFileAlt,
//   FaSignOutAlt,
// } from 'react-icons/fa';

// const sidebarItems = [
//   { name: 'Dashboard', href: '/admin', icon: <FaTachometerAlt /> },
//   { name: 'All Investors', href: '/admin/all-investors', icon: <FaUsers /> },
//   { name: 'All Subscriptions', href: '/admin/all-subscriptions', icon: <FaChartLine /> },
//   { name: 'All Referrals', href: '/admin/all-referrals', icon: <FaUserFriends /> },
//   { name: 'Payouts', href: '/admin/payouts', icon: <FaMoneyCheckAlt /> },
//   {name: 'Plans', href: '/admin/plans', icon: <FaFileAlt /> },
//   { name: 'Pages', href: '/admin/pages', icon: <FaFileAlt /> },
// ];

// const AdminSidebar: React.FC = () => {
//   const router = useRouter();
//   const pathname = usePathname();
//   const { user, logout } = useAuth();

//   const handleNavigation = (href: string) => {
//     router.push(href);
//   };

//   return (
//     <div className="w-64 h-screen bg-white dark:bg-gray-900 shadow-md flex flex-col justify-between transition-colors">
//       {/* Admin Profile Section */}
//       <div className="p-6 border-b border-gray-200 dark:border-gray-700">
//         <div className="flex items-center space-x-4">
//           <img
//             src="/logo1.png"
//             alt="Admin Avatar"
//             className="w-12 h-12 rounded-full object-cover"
//           />
//           <div>
//             <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{user?.name ?? 'Admin'}</p>
//             <p className="text-sm text-gray-500 dark:text-gray-400">Administrator</p>
//           </div>
//         </div>
//       </div>

//       {/* Navigation Links */}
//       <nav className="mt-4 flex-grow overflow-y-auto">
//         <ul>
//           {sidebarItems.map((item) => (
//             <li key={item.name}>
//               <motion.button
//                 onClick={() => handleNavigation(item.href)}
//                 className={`flex items-center w-full text-left px-6 py-3 mb-2 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none transition-colors duration-200 ${
//                   pathname === item.href ? 'bg-yellow-500 text-white' : ''
//                 }`}
//                 whileHover={{ scale: 1.02 }}
//                 whileTap={{ scale: 0.98 }}
//               >
//                 <span className="text-xl mr-3">{item.icon}</span>
//                 <span className="font-medium">{item.name}</span>
//               </motion.button>
//             </li>
//           ))}
//         </ul>
//       </nav>

//       {/* Logout Section */}
//       <div className="p-6 border-t border-gray-200 dark:border-gray-700">
//         <button
//           onClick={logout}
//           className="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 transition-colors duration-200"
//         >
//           <FaSignOutAlt className="mr-3 text-xl" />
//           <span className="font-medium">Logout</span>
//         </button>
//       </div>
//     </div>
//   );
// };

// export default AdminSidebar;

'use client';

import React from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { useAuth } from '@/context/AuthContext';
import {
  FaTachometerAlt,
  FaUsers,
  FaChartLine,
  FaUserFriends,
  FaMoneyCheckAlt,
  FaFileAlt,
  FaSignOutAlt,
  FaTimes,
} from 'react-icons/fa';

const sidebarItems = [
  { name: 'Dashboard', href: '/admin', icon: <FaTachometerAlt /> },
  { name: 'All Investors', href: '/admin/all-investors', icon: <FaUsers /> },
  { name: 'All Subscriptions', href: '/admin/all-subscriptions', icon: <FaChartLine /> },
  { name: 'All Referrals', href: '/admin/all-referrals', icon: <FaUserFriends /> },
  { name: 'Payouts', href: '/admin/payouts', icon: <FaMoneyCheckAlt /> },
  { name: 'Plans', href: '/admin/plans', icon: <FaFileAlt /> },
  { name: 'Pages', href: '/admin/pages', icon: <FaFileAlt /> },
];

interface AdminSidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ isOpen, toggleSidebar }) => {
  const router = useRouter();
  const pathname = usePathname();
  const { user, logout } = useAuth();

  const handleNavigation = (href: string) => {
    router.push(href);
    if (window.innerWidth < 768) {
      toggleSidebar();
    }
  };

  return (
    <div
      className={`fixed inset-y-0 left-0 z-40 w-64 bg-white dark:bg-gray-900 shadow-md flex flex-col justify-between transition-all duration-300 ease-in-out transform ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      } md:translate-x-0`}
    >
      {/* Close button for mobile */}
      <button
        className="md:hidden absolute top-4 right-4 text-gray-500 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-100"
        onClick={toggleSidebar}
      >
        <FaTimes className="h-6 w-6" />
      </button>

      {/* Admin Profile Section */}
      <div className="p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-4">
          <img
            src="/logo1.png"
            alt="Admin Avatar"
            className="w-12 h-12 rounded-full object-cover"
          />
          <div>
            <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{user?.name ?? 'Admin'}</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">Administrator</p>
          </div>
        </div>
      </div>

      {/* Navigation Links */}
      <nav className="mt-4 flex-grow overflow-y-auto">
        <ul>
          {sidebarItems.map((item) => (
            <li key={item.name}>
              <motion.button
                onClick={() => handleNavigation(item.href)}
                className={`flex items-center w-full text-left px-6 py-3 mb-2 text-gray-700 dark:text-gray-50 hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none transition-colors duration-200 ${
                  pathname === item.href ? 'bg-custom-orange text-white' : ''
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="text-xl mr-3">{item.icon}</span>
                <span className="font-medium">{item.name}</span>
              </motion.button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Logout Section */}
      <div className="p-6 border-t border-gray-200 dark:border-gray-700">
        <button
          onClick={logout}
          className="flex items-center text-gray-700 dark:text-gray-300 hover:text-red-600 transition-colors duration-200"
        >
          <FaSignOutAlt className="mr-3 text-xl" />
          <span className="font-medium">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default AdminSidebar;

